{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\seancefeedbacklist.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { Box, Typography, Paper, Divider, Dialog, DialogTitle, DialogContent, Stack, Button } from \"@mui/material\";\nimport { useTranslation } from 'react-i18next';\nimport axios from \"axios\";\nimport { DataGrid } from '@mui/x-data-grid';\nimport { Feedback as FeedbackIcon } from \"@mui/icons-material\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SeanceFeedbackList = () => {\n  _s();\n  const {\n    t\n  } = useTranslation('seances');\n  const {\n    id: seanceId\n  } = useParams();\n  const [feedbacks, setFeedbacks] = useState([]);\n  const [selectedFeedback, setSelectedFeedback] = useState(null);\n  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);\n  const reloadFeedbacks = () => {\n    if (seanceId) {\n      axios.get(`http://localhost:8000/feedback/seance/feedbacklist/${seanceId}`).then(res => {\n        console.log(\"Feedbacks reçus:\", res.data);\n        setFeedbacks(res.data);\n        // After setting feedbacks, send cleanup request to backend\n        const displayedIds = res.data.map(fb => fb.id).filter(id => id !== undefined);\n        axios.delete('http://localhost:8000/feedback-formateur/cleanup', {\n          data: {\n            formateurId: null,\n            // Set formateurId if available in context\n            seanceId: Number(seanceId),\n            keepIds: displayedIds\n          }\n        }).then(() => {\n          console.log('Cleanup request sent successfully');\n        }).catch(err => {\n          console.error('Error sending cleanup request:', err);\n        });\n      }).catch(err => console.error(\"Erreur chargement feedbacklist:\", err));\n    }\n  };\n  useEffect(() => {\n    reloadFeedbacks();\n  }, [seanceId]);\n\n  // Fonction pour transformer les données de feedback en format answers avec emojis\n  const createAnswersFromFeedback = feedback => {\n    if (!feedback) return [];\n    const answers = [];\n    if (feedback.sessionRating) answers.push({\n      question: 'Note de la session',\n      answer: feedback.sessionRating\n    });\n    if (feedback.contentQuality) answers.push({\n      question: 'Qualité du contenu',\n      answer: feedback.contentQuality\n    });\n    if (feedback.sessionOrganization) answers.push({\n      question: 'Organisation de la session',\n      answer: feedback.sessionOrganization\n    });\n    if (feedback.objectivesAchieved) answers.push({\n      question: 'Objectifs atteints',\n      answer: feedback.objectivesAchieved\n    });\n    if (feedback.sessionDuration) answers.push({\n      question: 'Durée de la séance',\n      answer: feedback.sessionDuration\n    });\n    if (feedback.trainerRating) answers.push({\n      question: 'Note du formateur',\n      answer: feedback.trainerRating\n    });\n    if (feedback.trainerClarity) answers.push({\n      question: 'Clarté du formateur',\n      answer: feedback.trainerClarity\n    });\n    if (feedback.trainerAvailability) answers.push({\n      question: 'Disponibilité du formateur',\n      answer: feedback.trainerAvailability\n    });\n    if (feedback.trainerPedagogy) answers.push({\n      question: 'Pédagogie du formateur',\n      answer: feedback.trainerPedagogy\n    });\n    if (feedback.trainerInteraction) answers.push({\n      question: 'Interaction du formateur',\n      answer: feedback.trainerInteraction\n    });\n    if (feedback.teamRating) answers.push({\n      question: 'Note de l\\'équipe',\n      answer: feedback.teamRating\n    });\n    if (feedback.teamCollaboration) answers.push({\n      question: 'Collaboration de l\\'équipe',\n      answer: feedback.teamCollaboration\n    });\n    if (feedback.teamParticipation) answers.push({\n      question: 'Participation de l\\'équipe',\n      answer: feedback.teamParticipation\n    });\n    if (feedback.teamCommunication) answers.push({\n      question: 'Communication de l\\'équipe',\n      answer: feedback.teamCommunication\n    });\n    if (feedback.sessionComments) answers.push({\n      question: 'Commentaires sur la session',\n      answer: feedback.sessionComments\n    });\n    if (feedback.trainerComments) answers.push({\n      question: 'Commentaires sur le formateur',\n      answer: feedback.trainerComments\n    });\n    if (feedback.teamComments) answers.push({\n      question: 'Commentaires sur l\\'équipe',\n      answer: feedback.teamComments\n    });\n    if (feedback.suggestions) answers.push({\n      question: 'Suggestions d\\'amélioration',\n      answer: feedback.suggestions\n    });\n    if (feedback.wouldRecommend) answers.push({\n      question: 'Recommanderiez-vous cette formation ?',\n      answer: feedback.wouldRecommend\n    });\n    return answers;\n  };\n  const feedbackColumns = [{\n    field: 'id',\n    headerName: t('id'),\n    width: 70\n  }, {\n    field: 'studentName',\n    headerName: t('studentName'),\n    width: 200\n  }, {\n    field: 'studentEmail',\n    headerName: t('studentEmail'),\n    width: 250\n  }, {\n    field: 'fullFeedback',\n    headerName: t('fullFeedback'),\n    width: 250,\n    renderCell: params => /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outlined\",\n      size: \"small\",\n      onClick: async () => {\n        try {\n          // Récupérer les détails complets du feedback\n          const response = await axios.get(`http://localhost:8000/feedback/seance/details/${seanceId}/${params.row.userId || params.row.id}`);\n          setSelectedFeedback(response.data);\n          setFeedbackDialogOpen(true);\n        } catch (error) {\n          console.error('Erreur lors de la récupération des détails:', error);\n          // Fallback: utiliser les données de base\n          setSelectedFeedback(params.row);\n          setFeedbackDialogOpen(true);\n        }\n      },\n      children: t('showMore')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'averageRating',\n    headerName: t('averageRating'),\n    width: 180,\n    renderCell: params => {\n      const avg = params.row.averageRating;\n      if (avg === null || avg === undefined) return t('noRating');\n      const rounded = Math.round(avg);\n      const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\n      const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 22\n          },\n          children: moodEmojis[rounded - 1]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            marginLeft: 4\n          },\n          children: moodLabels[rounded - 1]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#888',\n            marginLeft: 4\n          },\n          children: [\"(\", avg.toFixed(2), \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 2,\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(FeedbackIcon, {\n        fontSize: \"large\",\n        sx: {\n          verticalAlign: 'middle',\n          mr: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), t('feedbackList')]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 500,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: feedbacks,\n          columns: feedbackColumns,\n          pageSize: 7,\n          rowsPerPageOptions: [5, 10, 20],\n          disableSelectionOnClick: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: feedbackDialogOpen,\n      onClose: () => setFeedbackDialogOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FeedbackIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [t('feedbackFrom'), \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: selectedFeedback === null || selectedFeedback === void 0 ? void 0 : selectedFeedback.studentName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 35\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: selectedFeedback === null || selectedFeedback === void 0 ? void 0 : selectedFeedback.studentEmail\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          bgcolor: \"#f8fafc\",\n          maxHeight: 500\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: [t('date'), \": \", (selectedFeedback === null || selectedFeedback === void 0 ? void 0 : selectedFeedback.createdAt) && new Date(selectedFeedback.createdAt).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this), selectedFeedback ? (() => {\n            const answers = createAnswersFromFeedback(selectedFeedback);\n            if (answers.length === 0) {\n              return /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                sx: {\n                  textAlign: 'center',\n                  py: 3\n                },\n                children: t('noFeedbackData')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 19\n              }, this);\n            }\n\n            // Définition des sections thématiques\n            const sections = [{\n              title: t('sessionSection'),\n              keywords: ['note de la session', 'organisation', 'objectifs', 'durée', 'durée de la séance', 'qualité du contenu', 'commentaires sur la session']\n            }, {\n              title: t('trainerSection'),\n              keywords: ['note du formateur', 'clarté', 'disponibilité', 'pédagogie', 'interaction', 'commentaires sur le formateur']\n            }, {\n              title: t('teamSection'),\n              keywords: ['note de l\\'équipe', 'collaboration', 'participation', 'communication', 'commentaires sur l\\'équipe']\n            }, {\n              title: t('suggestionsSection'),\n              keywords: ['suggestions', 'amélioration', 'recommanderait']\n            }];\n\n            // Grouper les réponses par section avec un matching robuste\n            function normalize(str) {\n              return str.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') // retire les accents\n              .replace(/[^a-z0-9]/g, ''); // retire tout sauf lettres/chiffres\n            }\n            const groupedAnswers = answers.length > 0 ? sections.map(section => ({\n              ...section,\n              answers: answers.filter(qa => section.keywords.some(keyword => normalize(qa.question).includes(normalize(keyword))))\n            })) : [];\n\n            // Réponses non classées\n            const otherAnswers = answers.length > 0 ? answers.filter(qa => !sections.some(section => section.keywords.some(keyword => normalize(qa.question).includes(normalize(keyword))))) : [];\n\n            // Emoji/label pour toutes les réponses numériques (1-5)\n            const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\n            const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\n            return /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [groupedAnswers.map((section, idx) => section.answers.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Divider, {\n                  sx: {\n                    mb: 1\n                  },\n                  children: section.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  spacing: 2,\n                  children: section.answers.map((qa, qidx) => {\n                    let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\n                    let value = isNumeric ? Number(qa.answer) : null;\n                    return /*#__PURE__*/_jsxDEV(Paper, {\n                      elevation: 1,\n                      sx: {\n                        p: 2,\n                        bgcolor: \"#fff\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        fontWeight: \"bold\",\n                        gutterBottom: true,\n                        children: qa.question\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 252,\n                        columnNumber: 33\n                      }, this), isNumeric ? /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          fontSize: 32,\n                          children: moodEmojis[value - 1]\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 257,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          fontWeight: \"bold\",\n                          children: moodLabels[value - 1]\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 258,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          color: \"text.secondary\",\n                          children: [\"(\", value, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 259,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 256,\n                        columnNumber: 35\n                      }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                        style: {\n                          whiteSpace: 'pre-line'\n                        },\n                        children: qa.answer || t('noAnswer')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 262,\n                        columnNumber: 35\n                      }, this)]\n                    }, qidx, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 31\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 25\n                }, this)]\n              }, idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 23\n              }, this)), otherAnswers.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Divider, {\n                  sx: {\n                    mb: 1\n                  },\n                  children: t('otherSection')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  spacing: 2,\n                  children: otherAnswers.map((qa, qidx) => {\n                    let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\n                    let value = isNumeric ? Number(qa.answer) : null;\n                    return /*#__PURE__*/_jsxDEV(Paper, {\n                      elevation: 1,\n                      sx: {\n                        p: 2,\n                        bgcolor: \"#fff\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        fontWeight: \"bold\",\n                        gutterBottom: true,\n                        children: qa.question\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 280,\n                        columnNumber: 31\n                      }, this), isNumeric ? /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          fontSize: 32,\n                          children: moodEmojis[value - 1]\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 285,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          fontWeight: \"bold\",\n                          children: moodLabels[value - 1]\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 286,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          color: \"text.secondary\",\n                          children: [\"(\", value, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 287,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 284,\n                        columnNumber: 33\n                      }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                        style: {\n                          whiteSpace: 'pre-line'\n                        },\n                        children: qa.answer || t('noAnswer')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 290,\n                        columnNumber: 33\n                      }, this)]\n                    }, qidx, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 279,\n                      columnNumber: 29\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true);\n          })() : /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"text.secondary\",\n            sx: {\n              textAlign: 'center',\n              py: 3\n            },\n            children: \"Aucune donn\\xE9e de feedback disponible\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            children: t('averageRating')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), selectedFeedback ? (() => {\n            // Utiliser la note moyenne déjà calculée ou calculer à partir des réponses\n            if (selectedFeedback !== null && selectedFeedback !== void 0 && selectedFeedback.averageRating) {\n              const avg = selectedFeedback.averageRating;\n              const rounded = Math.round(avg);\n              const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\n              const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\n              return /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                mt: 1,\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  fontSize: 32,\n                  children: moodEmojis[rounded - 1]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  fontWeight: \"bold\",\n                  children: moodLabels[rounded - 1]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"text.secondary\",\n                  children: [\"(\", avg.toFixed(2), \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this);\n            } else {\n              // Récupère toutes les réponses numériques (1-5) depuis les answers transformées\n              const answers = createAnswersFromFeedback(selectedFeedback);\n              const numericAnswers = answers.map(qa => Number(qa.answer)).filter(val => !isNaN(val) && val >= 1 && val <= 5);\n              if (numericAnswers.length === 0) {\n                return /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"text.secondary\",\n                  children: t('noRating')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 26\n                }, this);\n              }\n              const avg = numericAnswers.reduce((a, b) => a + b, 0) / numericAnswers.length;\n              const rounded = Math.round(avg);\n              const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\n              const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\n              return /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                mt: 1,\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  fontSize: 32,\n                  children: moodEmojis[rounded - 1]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  fontWeight: \"bold\",\n                  children: moodLabels[rounded - 1]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  color: \"text.secondary\",\n                  children: [\"(\", avg.toFixed(2), \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this);\n            }\n          })() : /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"text.secondary\",\n            sx: {\n              textAlign: 'center',\n              py: 3\n            },\n            children: \"Aucune donn\\xE9e de feedback disponible\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(SeanceFeedbackList, \"pNQGhhrcD8e4HlzoelZrTU8avJQ=\", false, function () {\n  return [useTranslation, useParams];\n});\n_c = SeanceFeedbackList;\nexport default SeanceFeedbackList;\nvar _c;\n$RefreshReg$(_c, \"SeanceFeedbackList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "Box", "Typography", "Paper", "Divider", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "useTranslation", "axios", "DataGrid", "<PERSON><PERSON><PERSON>", "FeedbackIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SeanceFeedbackList", "_s", "t", "id", "seanceId", "feedbacks", "setFeedbacks", "selectedFeedback", "setSelectedFeedback", "feedbackDialogOpen", "setFeedbackDialogOpen", "reloadFeedbacks", "get", "then", "res", "console", "log", "data", "displayedIds", "map", "fb", "filter", "undefined", "delete", "formateurId", "Number", "keepIds", "catch", "err", "error", "createAnswersFromFeedback", "feedback", "answers", "sessionRating", "push", "question", "answer", "contentQuality", "sessionOrganization", "objectivesAchieved", "sessionDuration", "trainerRating", "trainerClarity", "trainerAvailability", "trainerPedagogy", "trainerInteraction", "teamRating", "teamCollaboration", "teamParticipation", "teamCommunication", "sessionComments", "trainerComments", "teamComments", "suggestions", "wouldRecommend", "feedbackColumns", "field", "headerName", "width", "renderCell", "params", "variant", "size", "onClick", "response", "row", "userId", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "avg", "averageRating", "rounded", "Math", "round", "moodEmojis", "<PERSON><PERSON><PERSON><PERSON>", "style", "display", "alignItems", "gap", "fontSize", "fontWeight", "marginLeft", "color", "toFixed", "p", "mb", "sx", "verticalAlign", "mr", "height", "rows", "columns", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "direction", "spacing", "studentName", "studentEmail", "dividers", "bgcolor", "maxHeight", "createdAt", "Date", "toLocaleString", "length", "textAlign", "py", "sections", "title", "keywords", "normalize", "str", "toLowerCase", "replace", "groupedAnswers", "section", "qa", "some", "keyword", "includes", "otherAnswers", "idx", "qidx", "isNumeric", "isNaN", "value", "elevation", "gutterBottom", "whiteSpace", "mt", "numericAnswers", "val", "reduce", "a", "b", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/seancefeedbacklist.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  Divider,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  Stack,\r\n  Button,\r\n} from \"@mui/material\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport axios from \"axios\";\r\nimport { DataGrid } from '@mui/x-data-grid';\r\nimport { Feedback as FeedbackIcon } from \"@mui/icons-material\";\r\n\r\nconst SeanceFeedbackList = () => {\r\n  const { t } = useTranslation('seances');\r\n  const { id: seanceId } = useParams();\r\n  const [feedbacks, setFeedbacks] = useState([]);\r\n  const [selectedFeedback, setSelectedFeedback] = useState(null);\r\n  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);\r\n\r\n  const reloadFeedbacks = () => {\r\n    if (seanceId) {\r\n      axios.get(`http://localhost:8000/feedback/seance/feedbacklist/${seanceId}`)\r\n        .then(res => {\r\n          console.log(\"Feedbacks reçus:\", res.data);\r\n          setFeedbacks(res.data);\r\n          // After setting feedbacks, send cleanup request to backend\r\n          const displayedIds = res.data.map(fb => fb.id).filter(id => id !== undefined);\r\n          axios.delete('http://localhost:8000/feedback-formateur/cleanup', {\r\n            data: {\r\n              formateurId: null, // Set formateurId if available in context\r\n              seanceId: Number(seanceId),\r\n              keepIds: displayedIds,\r\n            }\r\n          }).then(() => {\r\n            console.log('Cleanup request sent successfully');\r\n          }).catch(err => {\r\n            console.error('Error sending cleanup request:', err);\r\n          });\r\n        })\r\n        .catch(err => console.error(\"Erreur chargement feedbacklist:\", err));\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    reloadFeedbacks();\r\n  }, [seanceId]);\r\n\r\n  // Fonction pour transformer les données de feedback en format answers avec emojis\r\n  const createAnswersFromFeedback = (feedback) => {\r\n    if (!feedback) return [];\r\n\r\n    const answers = [];\r\n\r\n    if (feedback.sessionRating) answers.push({ question: 'Note de la session', answer: feedback.sessionRating });\r\n    if (feedback.contentQuality) answers.push({ question: 'Qualité du contenu', answer: feedback.contentQuality });\r\n    if (feedback.sessionOrganization) answers.push({ question: 'Organisation de la session', answer: feedback.sessionOrganization });\r\n    if (feedback.objectivesAchieved) answers.push({ question: 'Objectifs atteints', answer: feedback.objectivesAchieved });\r\n    if (feedback.sessionDuration) answers.push({ question: 'Durée de la séance', answer: feedback.sessionDuration });\r\n\r\n    if (feedback.trainerRating) answers.push({ question: 'Note du formateur', answer: feedback.trainerRating });\r\n    if (feedback.trainerClarity) answers.push({ question: 'Clarté du formateur', answer: feedback.trainerClarity });\r\n    if (feedback.trainerAvailability) answers.push({ question: 'Disponibilité du formateur', answer: feedback.trainerAvailability });\r\n    if (feedback.trainerPedagogy) answers.push({ question: 'Pédagogie du formateur', answer: feedback.trainerPedagogy });\r\n    if (feedback.trainerInteraction) answers.push({ question: 'Interaction du formateur', answer: feedback.trainerInteraction });\r\n\r\n    if (feedback.teamRating) answers.push({ question: 'Note de l\\'équipe', answer: feedback.teamRating });\r\n    if (feedback.teamCollaboration) answers.push({ question: 'Collaboration de l\\'équipe', answer: feedback.teamCollaboration });\r\n    if (feedback.teamParticipation) answers.push({ question: 'Participation de l\\'équipe', answer: feedback.teamParticipation });\r\n    if (feedback.teamCommunication) answers.push({ question: 'Communication de l\\'équipe', answer: feedback.teamCommunication });\r\n\r\n    if (feedback.sessionComments) answers.push({ question: 'Commentaires sur la session', answer: feedback.sessionComments });\r\n    if (feedback.trainerComments) answers.push({ question: 'Commentaires sur le formateur', answer: feedback.trainerComments });\r\n    if (feedback.teamComments) answers.push({ question: 'Commentaires sur l\\'équipe', answer: feedback.teamComments });\r\n    if (feedback.suggestions) answers.push({ question: 'Suggestions d\\'amélioration', answer: feedback.suggestions });\r\n    if (feedback.wouldRecommend) answers.push({ question: 'Recommanderiez-vous cette formation ?', answer: feedback.wouldRecommend });\r\n\r\n    return answers;\r\n  };\r\n\r\n  const feedbackColumns = [\r\n    { field: 'id', headerName: t('id'), width: 70 },\r\n    { field: 'studentName', headerName: t('studentName'), width: 200 },\r\n    { field: 'studentEmail', headerName: t('studentEmail'), width: 250 },\r\n    {\r\n      field: 'fullFeedback',\r\n      headerName: t('fullFeedback'),\r\n      width: 250,\r\n      renderCell: (params) => (\r\n        <Button\r\n          variant=\"outlined\"\r\n          size=\"small\"\r\n          onClick={async () => {\r\n            try {\r\n              // Récupérer les détails complets du feedback\r\n              const response = await axios.get(`http://localhost:8000/feedback/seance/details/${seanceId}/${params.row.userId || params.row.id}`);\r\n              setSelectedFeedback(response.data);\r\n              setFeedbackDialogOpen(true);\r\n            } catch (error) {\r\n              console.error('Erreur lors de la récupération des détails:', error);\r\n              // Fallback: utiliser les données de base\r\n              setSelectedFeedback(params.row);\r\n              setFeedbackDialogOpen(true);\r\n            }\r\n          }}\r\n        >\r\n          {t('showMore')}\r\n        </Button>\r\n      ),\r\n    },\r\n    {\r\n      field: 'averageRating',\r\n      headerName: t('averageRating'),\r\n      width: 180,\r\n      renderCell: (params) => {\r\n        const avg = params.row.averageRating;\r\n        if (avg === null || avg === undefined) return t('noRating');\r\n        const rounded = Math.round(avg);\r\n        const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\r\n        const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\r\n        return (\r\n          <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>\r\n            <span style={{ fontSize: 22 }}>{moodEmojis[rounded - 1]}</span>\r\n            <span style={{ fontWeight: 'bold', marginLeft: 4 }}>{moodLabels[rounded - 1]}</span>\r\n            <span style={{ color: '#888', marginLeft: 4 }}>({avg.toFixed(2)})</span>\r\n          </span>\r\n        );\r\n      }\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <Box p={2}>\r\n      <Typography variant=\"h4\" mb={3}>\r\n        <FeedbackIcon fontSize=\"large\" sx={{ verticalAlign: 'middle', mr: 2 }} />\r\n        {t('feedbackList')}\r\n      </Typography>\r\n\r\n      <Paper sx={{ p: 3 }}>\r\n        <Box sx={{ height: 500, width: '100%' }}>\r\n          <DataGrid\r\n            rows={feedbacks}\r\n            columns={feedbackColumns}\r\n            pageSize={7}\r\n            rowsPerPageOptions={[5, 10, 20]}\r\n            disableSelectionOnClick\r\n          />\r\n        </Box>\r\n      </Paper>\r\n\r\n      {/* Feedback Dialog */}\r\n      <Dialog open={feedbackDialogOpen} onClose={() => setFeedbackDialogOpen(false)} maxWidth=\"sm\" fullWidth>\r\n        <DialogTitle>\r\n          <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\r\n            <FeedbackIcon color=\"primary\" />\r\n            <Box>\r\n              {t('feedbackFrom')} <b>{selectedFeedback?.studentName}</b>\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                {selectedFeedback?.studentEmail}\r\n              </Typography>\r\n            </Box>\r\n          </Stack>\r\n        </DialogTitle>\r\n        <DialogContent dividers sx={{ bgcolor: \"#f8fafc\", maxHeight: 500 }}>\r\n          <Stack spacing={2}>\r\n            <Box>\r\n              <Typography variant=\"subtitle2\" color=\"text.secondary\">\r\n                {t('date')}: {selectedFeedback?.createdAt && new Date(selectedFeedback.createdAt).toLocaleString()}\r\n              </Typography>\r\n            </Box>\r\n            {selectedFeedback ? (() => {\r\n              const answers = createAnswersFromFeedback(selectedFeedback);\r\n\r\n              if (answers.length === 0) {\r\n                return (\r\n                  <Typography color=\"text.secondary\" sx={{ textAlign: 'center', py: 3 }}>\r\n                    {t('noFeedbackData')}\r\n                  </Typography>\r\n                );\r\n              }\r\n\r\n              // Définition des sections thématiques\r\n              const sections = [\r\n                {\r\n                  title: t('sessionSection'),\r\n                  keywords: [\r\n                    'note de la session',\r\n                    'organisation',\r\n                    'objectifs',\r\n                    'durée',\r\n                    'durée de la séance',\r\n                    'qualité du contenu',\r\n                    'commentaires sur la session'\r\n                  ]\r\n                },\r\n                {\r\n                  title: t('trainerSection'),\r\n                  keywords: ['note du formateur', 'clarté', 'disponibilité', 'pédagogie', 'interaction', 'commentaires sur le formateur']\r\n                },\r\n                {\r\n                  title: t('teamSection'),\r\n                  keywords: ['note de l\\'équipe', 'collaboration', 'participation', 'communication', 'commentaires sur l\\'équipe']\r\n                },\r\n                {\r\n                  title: t('suggestionsSection'),\r\n                  keywords: ['suggestions', 'amélioration', 'recommanderait']\r\n                }\r\n              ];\r\n\r\n              // Grouper les réponses par section avec un matching robuste\r\n              function normalize(str) {\r\n                return str\r\n                  .toLowerCase()\r\n                  .normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') // retire les accents\r\n                  .replace(/[^a-z0-9]/g, ''); // retire tout sauf lettres/chiffres\r\n              }\r\n\r\n              const groupedAnswers = answers.length > 0 ? sections.map(section => ({\r\n                ...section,\r\n                answers: answers.filter(qa =>\r\n                  section.keywords.some(keyword =>\r\n                    normalize(qa.question).includes(normalize(keyword))\r\n                  )\r\n                )\r\n              })) : [];\r\n\r\n              // Réponses non classées\r\n              const otherAnswers = answers.length > 0 ? answers.filter(qa =>\r\n                !sections.some(section => section.keywords.some(keyword => normalize(qa.question).includes(normalize(keyword))))\r\n              ) : [];\r\n\r\n              // Emoji/label pour toutes les réponses numériques (1-5)\r\n              const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\r\n              const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\r\n              return (\r\n                <>\r\n                  {groupedAnswers.map((section, idx) =>\r\n                    section.answers.length > 0 && (\r\n                      <Box key={idx} mb={2}>\r\n                        <Divider sx={{ mb: 1 }}>{section.title}</Divider>\r\n                        <Stack spacing={2}>\r\n                          {section.answers.map((qa, qidx) => {\r\n                            let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\r\n                            let value = isNumeric ? Number(qa.answer) : null;\r\n                            return (\r\n                              <Paper key={qidx} elevation={1} sx={{ p: 2, bgcolor: \"#fff\" }}>\r\n                                <Typography fontWeight=\"bold\" gutterBottom>\r\n                                  {qa.question}\r\n                                </Typography>\r\n                                {isNumeric ? (\r\n                                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                                    <Typography fontSize={32}>{moodEmojis[value - 1]}</Typography>\r\n                                    <Typography fontWeight=\"bold\">{moodLabels[value - 1]}</Typography>\r\n                                    <Typography color=\"text.secondary\">({value})</Typography>\r\n                                  </Box>\r\n                                ) : (\r\n                                  <Typography style={{ whiteSpace: 'pre-line' }}>{qa.answer || t('noAnswer')}</Typography>\r\n                                )}\r\n                              </Paper>\r\n                            );\r\n                          })}\r\n                        </Stack>\r\n                      </Box>\r\n                    )\r\n                  )}\r\n                  {otherAnswers.length > 0 && (\r\n                    <Box mb={2}>\r\n                      <Divider sx={{ mb: 1 }}>{t('otherSection')}</Divider>\r\n                      <Stack spacing={2}>\r\n                        {otherAnswers.map((qa, qidx) => {\r\n                          let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\r\n                          let value = isNumeric ? Number(qa.answer) : null;\r\n                          return (\r\n                            <Paper key={qidx} elevation={1} sx={{ p: 2, bgcolor: \"#fff\" }}>\r\n                              <Typography fontWeight=\"bold\" gutterBottom>\r\n                                {qa.question}\r\n                              </Typography>\r\n                              {isNumeric ? (\r\n                                <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                                  <Typography fontSize={32}>{moodEmojis[value - 1]}</Typography>\r\n                                  <Typography fontWeight=\"bold\">{moodLabels[value - 1]}</Typography>\r\n                                  <Typography color=\"text.secondary\">({value})</Typography>\r\n                                </Box>\r\n                              ) : (\r\n                                <Typography style={{ whiteSpace: 'pre-line' }}>{qa.answer || t('noAnswer')}</Typography>\r\n                              )}\r\n                            </Paper>\r\n                          );\r\n                        })}\r\n                      </Stack>\r\n                    </Box>\r\n                  )}\r\n                </>\r\n              );\r\n            })() : (\r\n              <Typography color=\"text.secondary\" sx={{ textAlign: 'center', py: 3 }}>\r\n                Aucune donnée de feedback disponible\r\n              </Typography>\r\n            )}\r\n            {/* Note moyenne de feedback */}\r\n            <Divider>{t('averageRating')}</Divider>\r\n            {selectedFeedback ? (() => {\r\n              // Utiliser la note moyenne déjà calculée ou calculer à partir des réponses\r\n              if (selectedFeedback?.averageRating) {\r\n                const avg = selectedFeedback.averageRating;\r\n                const rounded = Math.round(avg);\r\n                const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\r\n                const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\r\n                return (\r\n                  <Box display=\"flex\" alignItems=\"center\" gap={1} mt={1} mb={2}>\r\n                    <Typography fontSize={32}>{moodEmojis[rounded - 1]}</Typography>\r\n                    <Typography fontWeight=\"bold\">{moodLabels[rounded - 1]}</Typography>\r\n                    <Typography color=\"text.secondary\">({avg.toFixed(2)})</Typography>\r\n                  </Box>\r\n                );\r\n              } else {\r\n                // Récupère toutes les réponses numériques (1-5) depuis les answers transformées\r\n                const answers = createAnswersFromFeedback(selectedFeedback);\r\n                const numericAnswers = answers\r\n                  .map(qa => Number(qa.answer))\r\n                  .filter(val => !isNaN(val) && val >= 1 && val <= 5);\r\n                if (numericAnswers.length === 0) {\r\n                  return <Typography color=\"text.secondary\">{t('noRating')}</Typography>;\r\n                }\r\n                const avg = numericAnswers.reduce((a, b) => a + b, 0) / numericAnswers.length;\r\n                const rounded = Math.round(avg);\r\n                const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\r\n                const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\r\n                return (\r\n                  <Box display=\"flex\" alignItems=\"center\" gap={1} mt={1} mb={2}>\r\n                    <Typography fontSize={32}>{moodEmojis[rounded - 1]}</Typography>\r\n                    <Typography fontWeight=\"bold\">{moodLabels[rounded - 1]}</Typography>\r\n                    <Typography color=\"text.secondary\">({avg.toFixed(2)})</Typography>\r\n                  </Box>\r\n                );\r\n              }\r\n            })() : (\r\n              <Typography color=\"text.secondary\" sx={{ textAlign: 'center', py: 3 }}>\r\n                Aucune donnée de feedback disponible\r\n              </Typography>\r\n            )}\r\n          </Stack>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default SeanceFeedbackList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,QACD,eAAe;AACtB,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,QAAQ,IAAIC,YAAY,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/D,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAE,CAAC,GAAGX,cAAc,CAAC,SAAS,CAAC;EACvC,MAAM;IAAEY,EAAE,EAAEC;EAAS,CAAC,GAAGvB,SAAS,CAAC,CAAC;EACpC,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC6B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAM+B,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIP,QAAQ,EAAE;MACZZ,KAAK,CAACoB,GAAG,CAAC,sDAAsDR,QAAQ,EAAE,CAAC,CACxES,IAAI,CAACC,GAAG,IAAI;QACXC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,GAAG,CAACG,IAAI,CAAC;QACzCX,YAAY,CAACQ,GAAG,CAACG,IAAI,CAAC;QACtB;QACA,MAAMC,YAAY,GAAGJ,GAAG,CAACG,IAAI,CAACE,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACjB,EAAE,CAAC,CAACkB,MAAM,CAAClB,EAAE,IAAIA,EAAE,KAAKmB,SAAS,CAAC;QAC7E9B,KAAK,CAAC+B,MAAM,CAAC,kDAAkD,EAAE;UAC/DN,IAAI,EAAE;YACJO,WAAW,EAAE,IAAI;YAAE;YACnBpB,QAAQ,EAAEqB,MAAM,CAACrB,QAAQ,CAAC;YAC1BsB,OAAO,EAAER;UACX;QACF,CAAC,CAAC,CAACL,IAAI,CAAC,MAAM;UACZE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAClD,CAAC,CAAC,CAACW,KAAK,CAACC,GAAG,IAAI;UACdb,OAAO,CAACc,KAAK,CAAC,gCAAgC,EAAED,GAAG,CAAC;QACtD,CAAC,CAAC;MACJ,CAAC,CAAC,CACDD,KAAK,CAACC,GAAG,IAAIb,OAAO,CAACc,KAAK,CAAC,iCAAiC,EAAED,GAAG,CAAC,CAAC;IACxE;EACF,CAAC;EAEDjD,SAAS,CAAC,MAAM;IACdgC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM0B,yBAAyB,GAAIC,QAAQ,IAAK;IAC9C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,MAAMC,OAAO,GAAG,EAAE;IAElB,IAAID,QAAQ,CAACE,aAAa,EAAED,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,oBAAoB;MAAEC,MAAM,EAAEL,QAAQ,CAACE;IAAc,CAAC,CAAC;IAC5G,IAAIF,QAAQ,CAACM,cAAc,EAAEL,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,oBAAoB;MAAEC,MAAM,EAAEL,QAAQ,CAACM;IAAe,CAAC,CAAC;IAC9G,IAAIN,QAAQ,CAACO,mBAAmB,EAAEN,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEC,MAAM,EAAEL,QAAQ,CAACO;IAAoB,CAAC,CAAC;IAChI,IAAIP,QAAQ,CAACQ,kBAAkB,EAAEP,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,oBAAoB;MAAEC,MAAM,EAAEL,QAAQ,CAACQ;IAAmB,CAAC,CAAC;IACtH,IAAIR,QAAQ,CAACS,eAAe,EAAER,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,oBAAoB;MAAEC,MAAM,EAAEL,QAAQ,CAACS;IAAgB,CAAC,CAAC;IAEhH,IAAIT,QAAQ,CAACU,aAAa,EAAET,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEC,MAAM,EAAEL,QAAQ,CAACU;IAAc,CAAC,CAAC;IAC3G,IAAIV,QAAQ,CAACW,cAAc,EAAEV,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,qBAAqB;MAAEC,MAAM,EAAEL,QAAQ,CAACW;IAAe,CAAC,CAAC;IAC/G,IAAIX,QAAQ,CAACY,mBAAmB,EAAEX,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEC,MAAM,EAAEL,QAAQ,CAACY;IAAoB,CAAC,CAAC;IAChI,IAAIZ,QAAQ,CAACa,eAAe,EAAEZ,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,wBAAwB;MAAEC,MAAM,EAAEL,QAAQ,CAACa;IAAgB,CAAC,CAAC;IACpH,IAAIb,QAAQ,CAACc,kBAAkB,EAAEb,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,0BAA0B;MAAEC,MAAM,EAAEL,QAAQ,CAACc;IAAmB,CAAC,CAAC;IAE5H,IAAId,QAAQ,CAACe,UAAU,EAAEd,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEC,MAAM,EAAEL,QAAQ,CAACe;IAAW,CAAC,CAAC;IACrG,IAAIf,QAAQ,CAACgB,iBAAiB,EAAEf,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEC,MAAM,EAAEL,QAAQ,CAACgB;IAAkB,CAAC,CAAC;IAC5H,IAAIhB,QAAQ,CAACiB,iBAAiB,EAAEhB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEC,MAAM,EAAEL,QAAQ,CAACiB;IAAkB,CAAC,CAAC;IAC5H,IAAIjB,QAAQ,CAACkB,iBAAiB,EAAEjB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEC,MAAM,EAAEL,QAAQ,CAACkB;IAAkB,CAAC,CAAC;IAE5H,IAAIlB,QAAQ,CAACmB,eAAe,EAAElB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAEC,MAAM,EAAEL,QAAQ,CAACmB;IAAgB,CAAC,CAAC;IACzH,IAAInB,QAAQ,CAACoB,eAAe,EAAEnB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,+BAA+B;MAAEC,MAAM,EAAEL,QAAQ,CAACoB;IAAgB,CAAC,CAAC;IAC3H,IAAIpB,QAAQ,CAACqB,YAAY,EAAEpB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEC,MAAM,EAAEL,QAAQ,CAACqB;IAAa,CAAC,CAAC;IAClH,IAAIrB,QAAQ,CAACsB,WAAW,EAAErB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAEC,MAAM,EAAEL,QAAQ,CAACsB;IAAY,CAAC,CAAC;IACjH,IAAItB,QAAQ,CAACuB,cAAc,EAAEtB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,uCAAuC;MAAEC,MAAM,EAAEL,QAAQ,CAACuB;IAAe,CAAC,CAAC;IAEjI,OAAOtB,OAAO;EAChB,CAAC;EAED,MAAMuB,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAEvD,CAAC,CAAC,IAAI,CAAC;IAAEwD,KAAK,EAAE;EAAG,CAAC,EAC/C;IAAEF,KAAK,EAAE,aAAa;IAAEC,UAAU,EAAEvD,CAAC,CAAC,aAAa,CAAC;IAAEwD,KAAK,EAAE;EAAI,CAAC,EAClE;IAAEF,KAAK,EAAE,cAAc;IAAEC,UAAU,EAAEvD,CAAC,CAAC,cAAc,CAAC;IAAEwD,KAAK,EAAE;EAAI,CAAC,EACpE;IACEF,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAEvD,CAAC,CAAC,cAAc,CAAC;IAC7BwD,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM,iBACjB/D,OAAA,CAACP,MAAM;MACLuE,OAAO,EAAC,UAAU;MAClBC,IAAI,EAAC,OAAO;MACZC,OAAO,EAAE,MAAAA,CAAA,KAAY;QACnB,IAAI;UACF;UACA,MAAMC,QAAQ,GAAG,MAAMxE,KAAK,CAACoB,GAAG,CAAC,iDAAiDR,QAAQ,IAAIwD,MAAM,CAACK,GAAG,CAACC,MAAM,IAAIN,MAAM,CAACK,GAAG,CAAC9D,EAAE,EAAE,CAAC;UACnIK,mBAAmB,CAACwD,QAAQ,CAAC/C,IAAI,CAAC;UAClCP,qBAAqB,CAAC,IAAI,CAAC;QAC7B,CAAC,CAAC,OAAOmB,KAAK,EAAE;UACdd,OAAO,CAACc,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE;UACArB,mBAAmB,CAACoD,MAAM,CAACK,GAAG,CAAC;UAC/BvD,qBAAqB,CAAC,IAAI,CAAC;QAC7B;MACF,CAAE;MAAAyD,QAAA,EAEDjE,CAAC,CAAC,UAAU;IAAC;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEZ,CAAC,EACD;IACEf,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAEvD,CAAC,CAAC,eAAe,CAAC;IAC9BwD,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM,IAAK;MACtB,MAAMY,GAAG,GAAGZ,MAAM,CAACK,GAAG,CAACQ,aAAa;MACpC,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKlD,SAAS,EAAE,OAAOpB,CAAC,CAAC,UAAU,CAAC;MAC3D,MAAMwE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC;MAC/B,MAAMK,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACjD,MAAMC,UAAU,GAAG,CAAC5E,CAAC,CAAC,kBAAkB,CAAC,EAAEA,CAAC,CAAC,cAAc,CAAC,EAAEA,CAAC,CAAC,SAAS,CAAC,EAAEA,CAAC,CAAC,WAAW,CAAC,EAAEA,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/G,oBACEL,OAAA;QAAMkF,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAf,QAAA,gBAC7DtE,OAAA;UAAMkF,KAAK,EAAE;YAAEI,QAAQ,EAAE;UAAG,CAAE;UAAAhB,QAAA,EAAEU,UAAU,CAACH,OAAO,GAAG,CAAC;QAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/D1E,OAAA;UAAMkF,KAAK,EAAE;YAAEK,UAAU,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAE,CAAE;UAAAlB,QAAA,EAAEW,UAAU,CAACJ,OAAO,GAAG,CAAC;QAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpF1E,OAAA;UAAMkF,KAAK,EAAE;YAAEO,KAAK,EAAE,MAAM;YAAED,UAAU,EAAE;UAAE,CAAE;UAAAlB,QAAA,GAAC,GAAC,EAACK,GAAG,CAACe,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC;IAEX;EACF,CAAC,CACF;EAED,oBACE1E,OAAA,CAACf,GAAG;IAAC0G,CAAC,EAAE,CAAE;IAAArB,QAAA,gBACRtE,OAAA,CAACd,UAAU;MAAC8E,OAAO,EAAC,IAAI;MAAC4B,EAAE,EAAE,CAAE;MAAAtB,QAAA,gBAC7BtE,OAAA,CAACF,YAAY;QAACwF,QAAQ,EAAC,OAAO;QAACO,EAAE,EAAE;UAAEC,aAAa,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxErE,CAAC,CAAC,cAAc,CAAC;IAAA;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEb1E,OAAA,CAACb,KAAK;MAAC0G,EAAE,EAAE;QAAEF,CAAC,EAAE;MAAE,CAAE;MAAArB,QAAA,eAClBtE,OAAA,CAACf,GAAG;QAAC4G,EAAE,EAAE;UAAEG,MAAM,EAAE,GAAG;UAAEnC,KAAK,EAAE;QAAO,CAAE;QAAAS,QAAA,eACtCtE,OAAA,CAACJ,QAAQ;UACPqG,IAAI,EAAEzF,SAAU;UAChB0F,OAAO,EAAExC,eAAgB;UACzByC,QAAQ,EAAE,CAAE;UACZC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAE;UAChCC,uBAAuB;QAAA;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGR1E,OAAA,CAACX,MAAM;MAACiH,IAAI,EAAE1F,kBAAmB;MAAC2F,OAAO,EAAEA,CAAA,KAAM1F,qBAAqB,CAAC,KAAK,CAAE;MAAC2F,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAnC,QAAA,gBACpGtE,OAAA,CAACV,WAAW;QAAAgF,QAAA,eACVtE,OAAA,CAACR,KAAK;UAACkH,SAAS,EAAC,KAAK;UAACtB,UAAU,EAAC,QAAQ;UAACuB,OAAO,EAAE,CAAE;UAAArC,QAAA,gBACpDtE,OAAA,CAACF,YAAY;YAAC2F,KAAK,EAAC;UAAS;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChC1E,OAAA,CAACf,GAAG;YAAAqF,QAAA,GACDjE,CAAC,CAAC,cAAc,CAAC,EAAC,GAAC,eAAAL,OAAA;cAAAsE,QAAA,EAAI5D,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkG;YAAW;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1D1E,OAAA,CAACd,UAAU;cAAC8E,OAAO,EAAC,OAAO;cAACyB,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,EAC/C5D,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEmG;YAAY;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACd1E,OAAA,CAACT,aAAa;QAACuH,QAAQ;QAACjB,EAAE,EAAE;UAAEkB,OAAO,EAAE,SAAS;UAAEC,SAAS,EAAE;QAAI,CAAE;QAAA1C,QAAA,eACjEtE,OAAA,CAACR,KAAK;UAACmH,OAAO,EAAE,CAAE;UAAArC,QAAA,gBAChBtE,OAAA,CAACf,GAAG;YAAAqF,QAAA,eACFtE,OAAA,CAACd,UAAU;cAAC8E,OAAO,EAAC,WAAW;cAACyB,KAAK,EAAC,gBAAgB;cAAAnB,QAAA,GACnDjE,CAAC,CAAC,MAAM,CAAC,EAAC,IAAE,EAAC,CAAAK,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuG,SAAS,KAAI,IAAIC,IAAI,CAACxG,gBAAgB,CAACuG,SAAS,CAAC,CAACE,cAAc,CAAC,CAAC;YAAA;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EACLhE,gBAAgB,GAAG,CAAC,MAAM;YACzB,MAAMyB,OAAO,GAAGF,yBAAyB,CAACvB,gBAAgB,CAAC;YAE3D,IAAIyB,OAAO,CAACiF,MAAM,KAAK,CAAC,EAAE;cACxB,oBACEpH,OAAA,CAACd,UAAU;gBAACuG,KAAK,EAAC,gBAAgB;gBAACI,EAAE,EAAE;kBAAEwB,SAAS,EAAE,QAAQ;kBAAEC,EAAE,EAAE;gBAAE,CAAE;gBAAAhD,QAAA,EACnEjE,CAAC,CAAC,gBAAgB;cAAC;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAEjB;;YAEA;YACA,MAAM6C,QAAQ,GAAG,CACf;cACEC,KAAK,EAAEnH,CAAC,CAAC,gBAAgB,CAAC;cAC1BoH,QAAQ,EAAE,CACR,oBAAoB,EACpB,cAAc,EACd,WAAW,EACX,OAAO,EACP,oBAAoB,EACpB,oBAAoB,EACpB,6BAA6B;YAEjC,CAAC,EACD;cACED,KAAK,EAAEnH,CAAC,CAAC,gBAAgB,CAAC;cAC1BoH,QAAQ,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAE,+BAA+B;YACxH,CAAC,EACD;cACED,KAAK,EAAEnH,CAAC,CAAC,aAAa,CAAC;cACvBoH,QAAQ,EAAE,CAAC,mBAAmB,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,4BAA4B;YACjH,CAAC,EACD;cACED,KAAK,EAAEnH,CAAC,CAAC,oBAAoB,CAAC;cAC9BoH,QAAQ,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,gBAAgB;YAC5D,CAAC,CACF;;YAED;YACA,SAASC,SAASA,CAACC,GAAG,EAAE;cACtB,OAAOA,GAAG,CACPC,WAAW,CAAC,CAAC,CACbF,SAAS,CAAC,KAAK,CAAC,CAACG,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;cAAA,CACjDA,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;YAChC;YAEA,MAAMC,cAAc,GAAG3F,OAAO,CAACiF,MAAM,GAAG,CAAC,GAAGG,QAAQ,CAACjG,GAAG,CAACyG,OAAO,KAAK;cACnE,GAAGA,OAAO;cACV5F,OAAO,EAAEA,OAAO,CAACX,MAAM,CAACwG,EAAE,IACxBD,OAAO,CAACN,QAAQ,CAACQ,IAAI,CAACC,OAAO,IAC3BR,SAAS,CAACM,EAAE,CAAC1F,QAAQ,CAAC,CAAC6F,QAAQ,CAACT,SAAS,CAACQ,OAAO,CAAC,CACpD,CACF;YACF,CAAC,CAAC,CAAC,GAAG,EAAE;;YAER;YACA,MAAME,YAAY,GAAGjG,OAAO,CAACiF,MAAM,GAAG,CAAC,GAAGjF,OAAO,CAACX,MAAM,CAACwG,EAAE,IACzD,CAACT,QAAQ,CAACU,IAAI,CAACF,OAAO,IAAIA,OAAO,CAACN,QAAQ,CAACQ,IAAI,CAACC,OAAO,IAAIR,SAAS,CAACM,EAAE,CAAC1F,QAAQ,CAAC,CAAC6F,QAAQ,CAACT,SAAS,CAACQ,OAAO,CAAC,CAAC,CAAC,CACjH,CAAC,GAAG,EAAE;;YAEN;YACA,MAAMlD,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACjD,MAAMC,UAAU,GAAG,CAAC5E,CAAC,CAAC,kBAAkB,CAAC,EAAEA,CAAC,CAAC,cAAc,CAAC,EAAEA,CAAC,CAAC,SAAS,CAAC,EAAEA,CAAC,CAAC,WAAW,CAAC,EAAEA,CAAC,CAAC,eAAe,CAAC,CAAC;YAC/G,oBACEL,OAAA,CAAAE,SAAA;cAAAoE,QAAA,GACGwD,cAAc,CAACxG,GAAG,CAAC,CAACyG,OAAO,EAAEM,GAAG,KAC/BN,OAAO,CAAC5F,OAAO,CAACiF,MAAM,GAAG,CAAC,iBACxBpH,OAAA,CAACf,GAAG;gBAAW2G,EAAE,EAAE,CAAE;gBAAAtB,QAAA,gBACnBtE,OAAA,CAACZ,OAAO;kBAACyG,EAAE,EAAE;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAtB,QAAA,EAAEyD,OAAO,CAACP;gBAAK;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACjD1E,OAAA,CAACR,KAAK;kBAACmH,OAAO,EAAE,CAAE;kBAAArC,QAAA,EACfyD,OAAO,CAAC5F,OAAO,CAACb,GAAG,CAAC,CAAC0G,EAAE,EAAEM,IAAI,KAAK;oBACjC,IAAIC,SAAS,GAAG,CAACC,KAAK,CAAC5G,MAAM,CAACoG,EAAE,CAACzF,MAAM,CAAC,CAAC,IAAIX,MAAM,CAACoG,EAAE,CAACzF,MAAM,CAAC,IAAI,CAAC,IAAIX,MAAM,CAACoG,EAAE,CAACzF,MAAM,CAAC,IAAI,CAAC;oBAC7F,IAAIkG,KAAK,GAAGF,SAAS,GAAG3G,MAAM,CAACoG,EAAE,CAACzF,MAAM,CAAC,GAAG,IAAI;oBAChD,oBACEvC,OAAA,CAACb,KAAK;sBAAYuJ,SAAS,EAAE,CAAE;sBAAC7C,EAAE,EAAE;wBAAEF,CAAC,EAAE,CAAC;wBAAEoB,OAAO,EAAE;sBAAO,CAAE;sBAAAzC,QAAA,gBAC5DtE,OAAA,CAACd,UAAU;wBAACqG,UAAU,EAAC,MAAM;wBAACoD,YAAY;wBAAArE,QAAA,EACvC0D,EAAE,CAAC1F;sBAAQ;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,EACZ6D,SAAS,gBACRvI,OAAA,CAACf,GAAG;wBAACkG,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAACC,GAAG,EAAE,CAAE;wBAAAf,QAAA,gBAC7CtE,OAAA,CAACd,UAAU;0BAACoG,QAAQ,EAAE,EAAG;0BAAAhB,QAAA,EAAEU,UAAU,CAACyD,KAAK,GAAG,CAAC;wBAAC;0BAAAlE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC,eAC9D1E,OAAA,CAACd,UAAU;0BAACqG,UAAU,EAAC,MAAM;0BAAAjB,QAAA,EAAEW,UAAU,CAACwD,KAAK,GAAG,CAAC;wBAAC;0BAAAlE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC,eAClE1E,OAAA,CAACd,UAAU;0BAACuG,KAAK,EAAC,gBAAgB;0BAAAnB,QAAA,GAAC,GAAC,EAACmE,KAAK,EAAC,GAAC;wBAAA;0BAAAlE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,gBAEN1E,OAAA,CAACd,UAAU;wBAACgG,KAAK,EAAE;0BAAE0D,UAAU,EAAE;wBAAW,CAAE;wBAAAtE,QAAA,EAAE0D,EAAE,CAACzF,MAAM,IAAIlC,CAAC,CAAC,UAAU;sBAAC;wBAAAkE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CACxF;oBAAA,GAZS4D,IAAI;sBAAA/D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAaT,CAAC;kBAEZ,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA,GAvBA2D,GAAG;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBR,CAET,CAAC,EACA0D,YAAY,CAAChB,MAAM,GAAG,CAAC,iBACtBpH,OAAA,CAACf,GAAG;gBAAC2G,EAAE,EAAE,CAAE;gBAAAtB,QAAA,gBACTtE,OAAA,CAACZ,OAAO;kBAACyG,EAAE,EAAE;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAtB,QAAA,EAAEjE,CAAC,CAAC,cAAc;gBAAC;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACrD1E,OAAA,CAACR,KAAK;kBAACmH,OAAO,EAAE,CAAE;kBAAArC,QAAA,EACf8D,YAAY,CAAC9G,GAAG,CAAC,CAAC0G,EAAE,EAAEM,IAAI,KAAK;oBAC9B,IAAIC,SAAS,GAAG,CAACC,KAAK,CAAC5G,MAAM,CAACoG,EAAE,CAACzF,MAAM,CAAC,CAAC,IAAIX,MAAM,CAACoG,EAAE,CAACzF,MAAM,CAAC,IAAI,CAAC,IAAIX,MAAM,CAACoG,EAAE,CAACzF,MAAM,CAAC,IAAI,CAAC;oBAC7F,IAAIkG,KAAK,GAAGF,SAAS,GAAG3G,MAAM,CAACoG,EAAE,CAACzF,MAAM,CAAC,GAAG,IAAI;oBAChD,oBACEvC,OAAA,CAACb,KAAK;sBAAYuJ,SAAS,EAAE,CAAE;sBAAC7C,EAAE,EAAE;wBAAEF,CAAC,EAAE,CAAC;wBAAEoB,OAAO,EAAE;sBAAO,CAAE;sBAAAzC,QAAA,gBAC5DtE,OAAA,CAACd,UAAU;wBAACqG,UAAU,EAAC,MAAM;wBAACoD,YAAY;wBAAArE,QAAA,EACvC0D,EAAE,CAAC1F;sBAAQ;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,EACZ6D,SAAS,gBACRvI,OAAA,CAACf,GAAG;wBAACkG,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAACC,GAAG,EAAE,CAAE;wBAAAf,QAAA,gBAC7CtE,OAAA,CAACd,UAAU;0BAACoG,QAAQ,EAAE,EAAG;0BAAAhB,QAAA,EAAEU,UAAU,CAACyD,KAAK,GAAG,CAAC;wBAAC;0BAAAlE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC,eAC9D1E,OAAA,CAACd,UAAU;0BAACqG,UAAU,EAAC,MAAM;0BAAAjB,QAAA,EAAEW,UAAU,CAACwD,KAAK,GAAG,CAAC;wBAAC;0BAAAlE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC,eAClE1E,OAAA,CAACd,UAAU;0BAACuG,KAAK,EAAC,gBAAgB;0BAAAnB,QAAA,GAAC,GAAC,EAACmE,KAAK,EAAC,GAAC;wBAAA;0BAAAlE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,gBAEN1E,OAAA,CAACd,UAAU;wBAACgG,KAAK,EAAE;0BAAE0D,UAAU,EAAE;wBAAW,CAAE;wBAAAtE,QAAA,EAAE0D,EAAE,CAACzF,MAAM,IAAIlC,CAAC,CAAC,UAAU;sBAAC;wBAAAkE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CACxF;oBAAA,GAZS4D,IAAI;sBAAA/D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAaT,CAAC;kBAEZ,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACN;YAAA,eACD,CAAC;UAEP,CAAC,EAAE,CAAC,gBACF1E,OAAA,CAACd,UAAU;YAACuG,KAAK,EAAC,gBAAgB;YAACI,EAAE,EAAE;cAAEwB,SAAS,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAhD,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb,eAED1E,OAAA,CAACZ,OAAO;YAAAkF,QAAA,EAAEjE,CAAC,CAAC,eAAe;UAAC;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,EACtChE,gBAAgB,GAAG,CAAC,MAAM;YACzB;YACA,IAAIA,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEkE,aAAa,EAAE;cACnC,MAAMD,GAAG,GAAGjE,gBAAgB,CAACkE,aAAa;cAC1C,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC;cAC/B,MAAMK,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;cACjD,MAAMC,UAAU,GAAG,CAAC5E,CAAC,CAAC,kBAAkB,CAAC,EAAEA,CAAC,CAAC,cAAc,CAAC,EAAEA,CAAC,CAAC,SAAS,CAAC,EAAEA,CAAC,CAAC,WAAW,CAAC,EAAEA,CAAC,CAAC,eAAe,CAAC,CAAC;cAC/G,oBACEL,OAAA,CAACf,GAAG;gBAACkG,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACwD,EAAE,EAAE,CAAE;gBAACjD,EAAE,EAAE,CAAE;gBAAAtB,QAAA,gBAC3DtE,OAAA,CAACd,UAAU;kBAACoG,QAAQ,EAAE,EAAG;kBAAAhB,QAAA,EAAEU,UAAU,CAACH,OAAO,GAAG,CAAC;gBAAC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAChE1E,OAAA,CAACd,UAAU;kBAACqG,UAAU,EAAC,MAAM;kBAAAjB,QAAA,EAAEW,UAAU,CAACJ,OAAO,GAAG,CAAC;gBAAC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpE1E,OAAA,CAACd,UAAU;kBAACuG,KAAK,EAAC,gBAAgB;kBAAAnB,QAAA,GAAC,GAAC,EAACK,GAAG,CAACe,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAEV,CAAC,MAAM;cACL;cACA,MAAMvC,OAAO,GAAGF,yBAAyB,CAACvB,gBAAgB,CAAC;cAC3D,MAAMoI,cAAc,GAAG3G,OAAO,CAC3Bb,GAAG,CAAC0G,EAAE,IAAIpG,MAAM,CAACoG,EAAE,CAACzF,MAAM,CAAC,CAAC,CAC5Bf,MAAM,CAACuH,GAAG,IAAI,CAACP,KAAK,CAACO,GAAG,CAAC,IAAIA,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,CAAC;cACrD,IAAID,cAAc,CAAC1B,MAAM,KAAK,CAAC,EAAE;gBAC/B,oBAAOpH,OAAA,CAACd,UAAU;kBAACuG,KAAK,EAAC,gBAAgB;kBAAAnB,QAAA,EAAEjE,CAAC,CAAC,UAAU;gBAAC;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cACxE;cACA,MAAMC,GAAG,GAAGmE,cAAc,CAACE,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAGJ,cAAc,CAAC1B,MAAM;cAC7E,MAAMvC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC;cAC/B,MAAMK,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;cACjD,MAAMC,UAAU,GAAG,CAAC5E,CAAC,CAAC,kBAAkB,CAAC,EAAEA,CAAC,CAAC,cAAc,CAAC,EAAEA,CAAC,CAAC,SAAS,CAAC,EAAEA,CAAC,CAAC,WAAW,CAAC,EAAEA,CAAC,CAAC,eAAe,CAAC,CAAC;cAC/G,oBACEL,OAAA,CAACf,GAAG;gBAACkG,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACC,GAAG,EAAE,CAAE;gBAACwD,EAAE,EAAE,CAAE;gBAACjD,EAAE,EAAE,CAAE;gBAAAtB,QAAA,gBAC3DtE,OAAA,CAACd,UAAU;kBAACoG,QAAQ,EAAE,EAAG;kBAAAhB,QAAA,EAAEU,UAAU,CAACH,OAAO,GAAG,CAAC;gBAAC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAChE1E,OAAA,CAACd,UAAU;kBAACqG,UAAU,EAAC,MAAM;kBAAAjB,QAAA,EAAEW,UAAU,CAACJ,OAAO,GAAG,CAAC;gBAAC;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpE1E,OAAA,CAACd,UAAU;kBAACuG,KAAK,EAAC,gBAAgB;kBAAAnB,QAAA,GAAC,GAAC,EAACK,GAAG,CAACe,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAEV;UACF,CAAC,EAAE,CAAC,gBACF1E,OAAA,CAACd,UAAU;YAACuG,KAAK,EAAC,gBAAgB;YAACI,EAAE,EAAE;cAAEwB,SAAS,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAhD,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACtE,EAAA,CA7UID,kBAAkB;EAAA,QACRT,cAAc,EACHV,SAAS;AAAA;AAAAmK,EAAA,GAF9BhJ,kBAAkB;AA+UxB,eAAeA,kBAAkB;AAAC,IAAAgJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}