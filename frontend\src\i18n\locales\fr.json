{"common": {"add": "Ajouter", "cancel": "Annuler", "save": "Enregistrer", "saving": "Enregistrement...", "saveChanges": "Enregistrer les modifications", "updating": "Mise à jour...", "back": "Retour", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "programs": "Programmes", "noItemsFound": "Aucun élément trouvé", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "goHome": "Aller à l'accueil", "title": "Titre", "rememberMe": "Se souvenir de moi", "forgotPassword": "Mot de passe oublié ?", "login": "Se connecter", "appTitle": "Académie Maîtrise du Savoir", "logout": "Se déconnecter", "loading": "Chargement...", "hide": "Masquer", "details": "Détails", "close": "<PERSON><PERSON><PERSON>", "email": "Email", "actions": "Actions", "next": "Suivant", "changeLanguage": "Changer la langue", "associatedCourse": "Cours Associé", "associatedModule": "<PERSON><PERSON><PERSON>", "associatedProgram": "Programme Associé", "search": "<PERSON><PERSON><PERSON>"}, "role": {"etudiant": "Étudiant", "formateur": "Formateur", "createurdeformation": "Créateur de formation", "etablissement": "Responsable d'établissement", "admin": "Administrateur", "student": "Étudiant", "user": "Utilisa<PERSON>ur"}, "auth": {"emailRequired": "L'email est requis", "emailInvalid": "L'email est invalide", "passwordRequired": "Le mot de passe est requis", "passwordTooShort": "Le mot de passe doit contenir au moins 6 caractères", "loginFailed": "Échec de la connexion.", "accountNotVerified": "Votre compte n'est pas encore vérifié. Veuillez vérifier par SMS.", "login": "Connexion", "emailAddress": "<PERSON><PERSON><PERSON> email", "password": "Mot de passe", "invalidEmail": "Veu<PERSON>z entrer une adresse email valide.", "errorOccurred": "Une erreur s'est produite. Veuillez réessayer.", "emailSent": "<PERSON>ail envoy<PERSON>", "resetEmailSent": "Si un compte existe avec l'adresse email fournie, vous recevrez un email avec les instructions pour réinitialiser votre mot de passe.", "backToLogin": "Retour à la connexion", "forgotPassword": "Mot de passe oublié", "enterEmailForReset": "Entrez votre adresse email pour recevoir un lien de réinitialisation", "email": "Email", "sending": "Envoi en cours...", "sendLink": "Envoyer le lien"}, "dashboard": {"adminDashboard": "Dashboard – Admin", "participants": "Participants", "participationsThisMonth": "Participations ce mois", "formations": "Formations", "instructors": "Formateurs", "topFormations": "Top Formations (Plus suivies)", "students": "Étudiants", "monthlyParticipation": "Participation mensuelle (12 mois)", "topInstructors": "Top Formateurs (avis étudiants)", "topPartnerInstitutions": "Top Établissements partenaires"}, "notFound": {"title": "NotFound 404", "backToHome": "Retour à MKA"}, "buildProgram": {"overviewTitle": "Vue d'ensemble des programmes configurés", "searchProgram": "Rechercher un programme par nom", "program": "Programme", "level": "Niveau", "published": "<PERSON><PERSON><PERSON>", "publish": "Publier", "unpublish": "Dépublier", "publishSuccess": "Programme publié avec succès !", "unpublishSuccess": "Programme dépublié avec succès !", "publishError": "Erreur lors de la mise à jour du statut de publication.", "loadError": "Erreur chargement des programmes.", "buildProgram": "Construire un Programme", "modulesCoursesContents": "<PERSON><PERSON><PERSON>, cours et contenus", "programLevel": "Niveau du programme", "selectModules": "Sélectionner les modules", "contents": "<PERSON>tenus", "basic": "Basique", "intermediate": "Intermédiaire", "advanced": "<PERSON><PERSON><PERSON>", "fillAllFields": "<PERSON><PERSON><PERSON>z remplir tous les champs", "buildSuccess": "Programme construit avec succès !", "buildError": "Erreur lors de la construction du programme"}, "content": {"contentList": "Liste des contenus", "addContent": "Ajouter un contenu", "title": "Titre", "type": "Type", "file": "<PERSON><PERSON><PERSON>", "link": "<PERSON><PERSON>", "actions": "Actions", "takeQuiz": "<PERSON><PERSON><PERSON> le quiz", "view": "Voir", "noFile": "<PERSON><PERSON><PERSON>", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cet élément ?", "deleteError": "<PERSON><PERSON><PERSON> lors de <PERSON>", "titleRequired": "Le titre est requis", "fileRequired": "Un fichier est requis", "saveError": "Erreur lors de l'enregistrement", "contentType": "Type de contenu", "course": "Cours", "exercise": "Exercice", "quiz": "Quiz", "fileType": "Type de fichier", "image": "Image", "video": "Vidéo", "chooseFile": "<PERSON><PERSON> un fichier", "selectedFile": "<PERSON><PERSON><PERSON>", "quizNote": "Un quiz sera créé après l'enregistrement."}, "courses": {"courseList": "Liste des cours", "addCourse": "Ajouter un cours", "actions": "Actions", "viewContent": "Voir le contenu", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce cours ?", "createError": "Erreur !"}, "profile": {"editProfile": "Modifier le profil", "backToProfile": "Retour au profil", "clickCameraToChange": "Cliquez sur l'icône de l'appareil photo pour changer la photo", "personalInfo": "Informations personnelles", "fullName": "Nom complet", "email": "Email", "phone": "Téléphone", "location": "Localisation", "changePassword": "Changer le mot de passe", "professionalDetails": "Détails professionnels", "role": "R<PERSON><PERSON>", "contactAdminRole": "Contactez l'admin pour changer le rôle", "aboutMe": "À propos de moi", "skillsExpertise": "Compétences et expertise", "addSkill": "Ajouter une compétence", "changePasswordDescription": "Veuillez entrer votre mot de passe actuel et un nouveau mot de passe pour mettre à jour la sécurité de votre compte.", "currentPassword": "Mot de passe actuel", "newPassword": "Nouveau mot de passe", "confirmNewPassword": "Confirmer le nouveau mot de passe", "updatePassword": "Mettre à jour le mot de passe", "invalidImageFile": "Veuillez sélectionner un fichier image valide.", "fileSizeError": "La taille du fichier ne doit pas dépasser 5MB.", "blurryImageWarning": "Image détectée comme floue (qualité: {{quality}}). <PERSON><PERSON>z sur \"Utiliser quand même\" si vous souhaitez continuer.", "goodQualityImage": "Image de bonne qualité détectée (qualité: {{quality}}).", "imageAnalysisError": "Erreur lors de l'analyse de l'image. Veuillez réessayer.", "blurryImageAccepted": "Image floue acceptée. Nous recommandons d'utiliser une image plus nette pour de meilleurs résultats.", "currentPasswordRequired": "Le mot de passe actuel est requis", "newPasswordRequired": "Le nouveau mot de passe est requis", "passwordsDoNotMatch": "Les nouveaux mots de passe ne correspondent pas", "passwordTooShort": "Le nouveau mot de passe doit contenir au moins 6 caractères", "passwordChangedSuccess": "Mot de passe changé avec succès !", "passwordChangeError": "Échec du changement de mot de passe. Veuillez réessayer.", "confirmBlurryUpload": "Vous êtes sur le point de télécharger une image floue (qualité: {{quality}}). <PERSON>la pourrait affecter la qualité de votre profil. Voulez-vous continuer ?", "profileUpdatedSuccess": "Profil mis à jour avec succès !", "profileError": "<PERSON><PERSON><PERSON> de profil", "userNotFound": "Utilisateur non trouvé", "analyzingImageQuality": "Analyse de la qualité de l'image...", "blurryImageDetected": "Image floue détectée", "quality": "Qualité", "score": "Score", "recommendSharperImage": "Nous recommandons de choisir une image plus nette", "useAnyway": "Utiliser quand même", "passwordStrength": "Force du mot de passe", "strongPasswordTips": "Pour un mot de passe fort, incluez", "atLeast8Chars": "Au moins 8 caractères", "oneUppercase": "Une lettre majuscule", "oneLowercase": "Une lettre minuscule", "oneDigit": "Un chiffre", "oneSpecialChar": "Un caractère spécial (!@#$%...)", "passwordStrengthWeak": "faible", "passwordStrengthMedium": "moyenne", "passwordStrengthStrong": "forte", "missingUserData": "ID utilisateur manquant et aucune donnée utilisateur disponible", "invalidUserId": "ID utilisateur invalide", "loadUserError": "Impossible de charger les informations de l'utilisateur.", "userProfile": "Profil utilisateur", "noBio": "Aucune bio fournie", "notProvided": "Non fourni", "locationNotSpecified": "Localisation non spécifiée", "noSkillsAdded": "Aucune compétence ajoutée.", "viewProfile": "Voir le profil", "profileUpdatePartialError": "Profil mis à jour mais échec du téléchargement de la photo de profil", "profileUpdateError": "Échec de la mise à jour du profil", "goodImageQuality": "Image de bonne qualité", "passwordChangeEmailSent": "Un email de confirmation a été envoyé à votre adresse email"}, "feedback": {"feedbackCenter": "Centre de commentaires", "submitFeedback": "Soumettre un commentaire", "viewFeedback": "Voir les commentaires", "analytics": {"title": "Analyses des commentaires", "timeRange": "Plage de temps", "last30Days": "30 derniers jours", "last3Months": "3 derniers mois", "last6Months": "6 derniers mois", "lastYear": "<PERSON><PERSON><PERSON> ann<PERSON>", "allTime": "Tout le temps", "ratingDistribution": "Distribution des notes", "categoryDistribution": "Distribution par catégorie", "feedbackTimeline": "Chronologie des commentaires", "numberOfRatings": "Nombre de notes", "feedbackCount": "Nombre de commentaires", "keyInsights": "Insights clés", "insight1": "La plupart des commentaires concernent le contenu du cours (43%)", "insight2": "La note moyenne s'est améliorée de 0,5 étoile au cours des 3 derniers mois", "insight3": "Les problèmes techniques ont diminué de 30% depuis le dernier trimestre"}, "totalFeedbacks": "Total des commentaires", "averageRating": "<PERSON> moyenne", "recentFeedback": "Commentaires récents", "pendingResponses": "Réponses en attente", "categoryBreakdown": "Commentaires par catégorie", "last7Days": "7 derniers jours", "categories": {"course": "Contenu du cours", "instructor": "In<PERSON><PERSON><PERSON><PERSON>", "technical": "Problème technique", "platform": "Plateforme", "suggestion": "Suggestion"}, "sender": "Expéditeur", "receiver": "<PERSON><PERSON><PERSON>", "feedbackType": "Type de commentaire", "general": "Général", "studentToInstructor": "Étudiant → Instructeur", "instructorToStudent": "Instructeur → Étudiant", "courseFeedback": "Commentaire de cours", "category": "<PERSON><PERSON><PERSON><PERSON>", "tags": "Étiquettes", "addTags": "Ajouter des étiquettes", "message": "Message", "rating": "Note", "sendFeedback": "Envoyer le commentaire", "submitSuccess": "Commentaire soumis avec succès !", "submitError": "Échec de la soumission du commentaire. Veuillez réessayer.", "search": "Rechercher des commentaires...", "filterByType": "Filtrer par type", "filterByCategory": "Filtrer par catégorie", "all": "Tous", "from": "De", "to": "À", "feedbackTypes": {"bug": "Signalement de Bug", "feature": "<PERSON><PERSON><PERSON> de Fonctionnalité", "improvement": "Amélioration", "complaint": "Réclamation"}, "priorities": {"low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "Élevée", "critical": "Critique"}, "steps": {"type": "Type de Feedback", "category": "Catégorie & Priorité", "description": "Description Détaillée", "technical": "Informations Techniques", "contact": "Contact & Finalisation"}, "form": {"title": "<PERSON><PERSON>re du <PERSON>", "description": "Description détail<PERSON>e", "stepsToReproduce": "Étapes pour reproduire", "expectedBehavior": "Comportement attendu", "actualBehavior": "Comportement actuel", "browser": "Navigateur utilisé", "device": "Appareil utilisé", "contactInfo": "Informations de contact", "allowContact": "J'autorise l'équipe à me recontacter si nécessaire"}, "success": "Merci pour votre feedback ! Nous avons bien reçu votre demande et nous l'examinerons dans les plus brefs délais.", "preview": "Aperçu de votre feedback", "noFeedbackFound": "Aucun commentaire trouvé correspondant à vos critères", "like": "<PERSON>'aime", "dislike": "Je n'aime pas", "report": "Signaler", "respond": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reportSubmitted": "Signalement soumis", "showing": "Affichage", "of": "de", "items": "éléments", "response": {"respondToFeedback": "Répondre au commentaire", "yourResponse": "Votre réponse", "enterResponse": "Entrez votre réponse ici...", "responseGuidelines": "Directives : <PERSON><PERSON><PERSON>, abordez tous les points soulevés et fournissez des commentaires constructifs.", "sending": "Envoi...", "sendResponse": "Envoyer la réponse", "emptyResponseError": "Veuillez entrer une réponse", "submitError": "Échec de la soumission de la réponse. Veuillez réessayer.", "responseSubmitted": "Réponse soumise avec succès !"}}, "modules": {"moduleList": "Liste des Modules", "addModule": "<PERSON><PERSON><PERSON> Mo<PERSON>", "moduleName": "Nom du Module", "period": "Période", "duration": "<PERSON><PERSON><PERSON>", "actions": "Actions", "viewCourses": "Voir Cours", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce module ?", "periodUnit": "Unité de Période", "day": "Jour", "week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON>", "addSuccess": "Module ajouté avec succès!", "addError": "<PERSON><PERSON>ur lors de l'ajout du module"}, "quiz": {"takeQuiz": "<PERSON><PERSON> le Quiz", "timeRemaining": "Temps restant", "hidden": "<PERSON><PERSON><PERSON><PERSON>", "hideTimer": "Masquer le chrono", "showTimer": "<PERSON><PERSON><PERSON><PERSON> le chrono", "question": "Question", "questionImage": "Image de la question", "choice": "<PERSON><PERSON>", "yourAnswer": "Votre réponse", "timeExpired": "Temps écoulé ! Le quiz a été soumis automatiquement.", "submitQuiz": "Soumettre le Quiz", "createQuiz": "<PERSON><PERSON>er un Quiz", "editQuiz": "Modifier le Quiz", "loadError": "Erreur de chargement du quiz", "loading": "Chargement...", "questionText": "Texte de la Question", "type": "Type", "score": "Score", "addImage": "Ajouter Image", "correctAnswer": "Réponse Correcte", "answerChoices": "Choix de Réponses", "duration": "<PERSON><PERSON><PERSON> (minutes)", "addQuestion": "Ajouter Question", "saveQuiz": "Enregistrer Quiz", "imageUploadError": "E<PERSON>ur de téléchargement d'image", "questionRequired": "La question doit avoir un texte", "correctAnswerRequired": "La question doit avoir au moins une bonne réponse", "choiceTextRequired": "Tous les choix doivent avoir un texte ou une image", "fillBlankAnswerRequired": "La question à remplir doit avoir une réponse correcte", "quizCreatedSuccess": "Quiz créé avec succès!", "quizUpdatedSuccess": "Quiz modifié avec succès!", "submitError": "Échec lors de la soumission du quiz", "addChoiceImage": "Ajouter une image", "correctChoice": "Bonne réponse", "markCorrect": "<PERSON><PERSON> correcte", "deleteChoice": "Supprimer ce choix", "addChoice": "Ajouter un choix", "mcq": "<PERSON><PERSON>", "trueFalse": "Vrai/Faux", "fillBlank": "<PERSON><PERSON><PERSON><PERSON>", "imageChoice": "Choix d'Image", "true": "Vrai", "false": "Faux"}, "resetPassword": {"title": "Réinitialiser le mot de passe", "subtitle": "Entrez un nouveau mot de passe sécurisé", "newPassword": "Nouveau mot de passe", "confirmPassword": "Confirmer le mot de passe", "strength": "Force", "weak": "faible", "medium": "moyenne", "strong": "forte", "passwordsDoNotMatch": "Les mots de passe ne correspondent pas.", "invalidToken": "Lien invalide ou expiré.", "successMessage": "Mot de passe mis à jour avec succès.", "errorMessage": "Une erreur est survenue. Vérifiez le lien ou réessayez.", "securityTips": "Pour une sécurité optimale, utilisez", "atLeast8Chars": "Au moins 8 caractères", "upperLowerCase": "Une lettre majuscule et une minuscule", "oneDigit": "Un chiffre", "specialChar": "Un caractère spécial (ex: ! @ # $)", "resetting": "Réinitialisation", "resetButton": "Réinitialiser le mot de passe"}, "seanceFormateur": {"title": "<PERSON><PERSON><PERSON>", "manageMySessions": "<PERSON><PERSON><PERSON>", "manageSessionsOf": "🎓 <PERSON><PERSON><PERSON> mes <PERSON> de la <PERSON>", "manageSessionsOfSession2": "🎓 <PERSON><PERSON><PERSON> mes <PERSON> {{sessionId}} 🎓"}, "session": {"addSession": "Ajouter une session", "sessionList": "Liste des sessions"}, "studentLanding": {"title": "Choisir un Programme", "welcome": "Bienvenue ! Sélectionnez un programme ci-dessous pour commencer à apprendre.", "noDescription": "Aucune description fournie.", "viewProgram": "Voir le Programme"}, "studentProgram": {"title": "Aperçu du Programme", "allPrograms": "Tous les Programmes", "noModules": "Aucun module disponible.", "noCourses": "Aucun cours dans ce module.", "type": "Type", "viewCourse": "Voir le Cours", "completed": "<PERSON><PERSON><PERSON><PERSON>", "markAsDone": "<PERSON><PERSON> comme Fait"}, "seances": {"mySessions": "<PERSON><PERSON>", "noSessions": "Aucune séance pour le moment.", "program": "Programme", "animateSession": "<PERSON><PERSON><PERSON>", "programDetails": "Détails du programme", "module": "<PERSON><PERSON><PERSON>", "course": "Cours", "content": "Contenu", "confirmDelete": "Êtes-vous sûr de vouloir supprimer cette séance ?", "uploadImageError": "Erreur lors du téléchargement de l'image", "uploadVideoError": "Erreur lors du téléchargement de la vidéo", "saveSuccess": "<PERSON><PERSON><PERSON> sauvegard<PERSON> avec succès !", "loadingProgram": "Chargement du programme...", "loadingSession": "Chargement de la séance...", "hide": "Masquer", "show": "<PERSON><PERSON><PERSON><PERSON>", "hideHierarchy": "Masquer la hiérarchie", "showHierarchy": "Afficher la hiérarchie", "sessionAdditions": "<PERSON><PERSON><PERSON> séance", "quizComing": "Quiz (à venir)", "notesChat": "Notes / Chat", "whiteboard": "Tableau blanc", "feedback": "<PERSON><PERSON><PERSON>", "sessionImages": "Images propres à la séance", "sessionVideos": "Vidéos propres à la séance", "sessionNotes": "Notes propres à la séance", "notesPlaceholder": "Prends tes notes ici...", "saving": "Sauvegarde...", "saveSession": "Sauvegarder la séance", "quizFeature": "La fonctionnalité 'Quiz' arrive bientôt !", "sessionChat": "Chat de s<PERSON>", "writeMessage": "É<PERSON>ris un message...", "send": "Envoyer", "fileReady": "<PERSON><PERSON><PERSON> prêt à envoyer", "anonymous": "Anonyme", "sessionFeedback": "<PERSON><PERSON><PERSON> de <PERSON>", "hideFeedback": "Masquer le feedback", "showFeedback": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>", "feedbackInstructions": "Cliquez sur 'Afficher le feedback' pour remplir le formulaire d'évaluation de la séance", "unpublish": "Dépublier", "publish": "Publier", "statusChangeError": "Erreur lors du changement de statut.", "fileUploadError": "Erreur upload fichier", "deleteMessageError": "Suppression impossible (vous n'êtes pas l'auteur du message)", "sessionsList": "S<PERSON><PERSON><PERSON> de cette session", "refresh": "Actualiser", "loading": "Chargement...", "defaultRoom": "Salle par défaut", "showMore": "Voir plus", "fullFeedback": "Voir le feedback", "studentName": "Nom de l'étudiant", "studentEmail": "Email de l'étudiant", "averageRating": "<PERSON> moyenne", "noRating": "Aucune note", "veryDissatisfied": "Très insatisfait", "dissatisfied": "Insatisfait", "neutral": "Neutre", "satisfied": "Satisfait", "verySatisfied": "<PERSON><PERSON><PERSON> satisfait", "feedbackFrom": "<PERSON><PERSON><PERSON> de", "date": "Date", "sessionSection": "Session", "trainerSection": "Formateur", "teamSection": "Équipe", "suggestionsSection": "Suggestions", "otherSection": "Autres réponses", "noAnswer": "Pas de réponse", "feedbackList": "Liste des feedbacks", "feedbackDetails": "<PERSON><PERSON><PERSON> du <PERSON>", "noFeedbackSelected": "Aucun feedback sélectionné", "sessionFeedbackList": "Liste des feedbacks de session", "noFeedback": "Aucun feedback", "generalFeedback": "<PERSON>ed<PERSON>", "noDetailedFeedback": "Aucun détail de feedback disponible", "feedbackSummary": "R<PERSON><PERSON><PERSON>", "feedbackCount": "feedback(s)", "close": "<PERSON><PERSON><PERSON>", "export": "Exporter", "sessionFeedbackDetails": "<PERSON><PERSON><PERSON> du feedback de session", "feedbackId": "ID du feedback", "mainFeedback": "Feedback principal", "detailedComments": "Commentaires détaillés", "detailedRatings": "Notes détaillées", "formResponses": "Réponses du formulaire", "sessionRating": "Note de la session", "contentQuality": "Qualité du contenu", "trainerRating": "Note du formateur", "teamRating": "Note de l'équipe"}, "addSeance": {"title": "Titre", "titleField": "<PERSON><PERSON><PERSON> de la séance", "date": "Date", "time": "<PERSON><PERSON>", "program": "Programme", "programPreview": "Aperçu du Programme", "createButton": "<PERSON><PERSON><PERSON> la Séance", "fillAllFields": "Veu<PERSON>z remplir tous les champs obligatoires.", "creationError": "Erreur lors de la création de la séance. Veuillez réessayer.", "hideForm": "Masquer le formulaire", "createNewSession": "<PERSON><PERSON>er une nouvelle séance", "createNewSessionTitle": "<PERSON><PERSON>er une nouvelle séance", "sessionPreview": "Aperçu de la session sélectionnée", "createSessionButton": "CRÉER LA SÉANCE", "fillAllFieldsAlert": "Remplissez tous les champs.", "creationErrorAlert": "E<PERSON>ur lors de la création de la séance"}, "sessions": {"sessionList": "Liste des Sessions", "addSession": "<PERSON><PERSON><PERSON> une nouvelle session", "noSessions": "Aucune session enregistrée.", "publishedProgram": "Programme publié", "sessionName": "Nom de la session", "startDate": "Date de début", "endDate": "Date de fin", "uploadImage": "Télécharger une image", "selectedImage": "Image sélectionnée", "programPreview": "Aperçu du programme", "saveSession": "Enregistrer la session", "fillAllFields": "Veuillez remplir tous les champs obligatoires", "startBeforeEnd": "La date de début doit être antérieure à la date de fin", "sessionSaved": "Session enregistrée avec succès !", "saveError": "Erreur lors de l'enregistrement de la session", "loadError": "Erreur lors du chargement des sessions", "deleteSuccess": "Session supprimée avec succès !", "deleteError": "<PERSON><PERSON><PERSON> lors de <PERSON>", "share": "Partager", "shareSession": "Partager la session", "sharePreview": "Prévisualisation de votre publication sur les réseaux sociaux", "customizePost": "Personnalisez votre publication", "choosePlatform": "Choisissez votre plateforme", "copyText": "<PERSON><PERSON><PERSON> le texte", "textCopied": "Texte copié dans le presse-papiers!", "copyError": "<PERSON><PERSON>ur lors de la copie", "program": "Programme", "period": "<PERSON>", "to": "au", "unknown": "Inconnu", "modulesContent": "<PERSON><PERSON><PERSON> et Contenus", "downloadImage": "Télécharger l'image", "newOpportunity": "Nouvelle opportunité à ne pas rater!", "delete": "<PERSON><PERSON><PERSON><PERSON>", "join": "Rejoindre", "addUser": "Ajouter un utilisateur", "feedback": "<PERSON><PERSON><PERSON>", "hideFeedback": "Ma<PERSON><PERSON>back", "sessionFeedback": "<PERSON><PERSON><PERSON> <PERSON>", "userEmailPlaceholder": "<PERSON><PERSON> utilisa<PERSON>", "add": "Ajouter", "adding": "Ajout...", "cancel": "Annuler", "enterEmail": "Veuillez entrer un email.", "userAdded": "Utilisateur ajouté à la session !", "addUserError": "<PERSON><PERSON>ur lors de l'ajout de l'utilisateur"}, "users": {"loadError": "Erreur lors du chargement des utilisateurs", "userList": "Liste des Utilisateurs", "deleteSuccess": "Compte supprimé avec succès", "deleteError": "<PERSON><PERSON><PERSON> lors de la suppression du compte", "statusUpdateSuccess": "Statut du compte mis à jour avec succès", "statusUpdateError": "Erreur lors de la mise à jour du statut du compte", "confirmDelete": "Confirmer la <PERSON>", "confirmToggleStatus": "Êtes-vous sûr de vouloir modifier le statut de cet utilisateur ?", "searchUsers": "Rechercher des utilisateurs...", "totalUsers": "Total Utilisateurs", "activeUsers": "Utilisateurs Actifs", "inactiveUsers": "Utilisateurs Inactifs", "inactiveAccounts": "Comptes <PERSON>", "name": "Nom", "email": "Email", "role": "R<PERSON><PERSON>", "status": "Statut", "actions": "Actions", "active": "Actif", "inactive": "Inactif", "view": "Voir", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "activate": "Activer", "deactivate": "Désactiver", "refresh": "Actualiser", "addUser": "<PERSON><PERSON><PERSON>", "blockedAccounts": "<PERSON><PERSON><PERSON>", "pendingAccounts": "Comptes en Attente", "verifiedAccounts": "Comptes Vérifiés", "userManagement": "Gestion des Utilisateurs", "adminDashboard": "Tableau de bord Admin", "activeAccounts": "Comptes Actifs", "activityRate": "Taux d'Activité", "searchPlaceholder": "Rechercher des utilisateurs...", "noUsersFound": "Aucun utilisateur trouvé", "noSearchResults": "Aucun résultat pour cette recherche", "startByAddingUser": "Commencez par ajouter un utilisateur", "user": "Utilisa<PERSON>ur", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer l'utilisateur   ?", "irreversibleAction": "Cette action est irréversible.", "deleteButton": "<PERSON><PERSON><PERSON><PERSON>", "confirmToggleTitleActive": "Désactiver le compte", "confirmToggleTitleInactive": "<PERSON><PERSON> le compte", "confirmToggleMessageActive": "<PERSON><PERSON><PERSON>r le compte de {{name}} ({{email}}) ?", "confirmToggleMessageInactive": "Activer le compte de {{name}} ({{email}}) ?", "activateAccount": "<PERSON><PERSON> le compte", "deactivateAccount": "Désactiver le compte", "activateUser": "Activer", "deactivateUser": "Désactiver", "currentStatus": "Statut actuel", "activeStatus": "Actif", "inactiveStatus": "Inactif", "id": "ID", "usersSection": "Section Utilisateurs", "invalidEmail": "<PERSON><PERSON><PERSON> email invalide.", "createSuccess": "Utilisateur créé avec succès !", "emailInvalidOrUndeliverable": "L'email est invalide ou non distribuable.", "userAlreadyExists": "Cet utilisateur existe déjà.", "createError": "<PERSON><PERSON>ur lors de la création de l'utilisateur.", "emailPlaceholder": "Entrez l'email de l'utilisateur", "phonePlaceholder": "Entrez le numéro de téléphone", "creating": "Création en cours...", "assignSessions": "Sessions à assigner", "noSessionsAvailable": "Aucune session disponible", "selectMultipleSessions": "Vous pouvez cocher une ou plusieurs sessions."}, "programs": {"programList": "Liste des Programmes", "programName": "Nom du Programme", "addProgram": "Ajouter Programme", "viewProgram": "Voir Programme", "viewPrograms": "Voir Programmes", "confirmDelete": "Êtes-vous sûr de vouloir supprimer ce programme ?"}, "sidebar": {"home": "Accueil", "users": "Utilisateurs", "modules": "<PERSON><PERSON><PERSON>", "courses": "Cours", "contents": "<PERSON>tenus", "programs": "Programmes", "programsOverview": "Vue Programmes", "sessions": "Sessions", "seances": "Séances", "feedback": "Commentaires"}, "table": {"noRows": "<PERSON><PERSON><PERSON> ligne", "id": "ID", "rowsPerPage": "<PERSON><PERSON><PERSON> par page :"}, "countries": {"france": "France", "usa": "États-Unis", "uk": "Royaume-Uni", "germany": "Allemagne", "spain": "<PERSON><PERSON><PERSON><PERSON>", "italy": "Italie", "morocco": "Maroc", "algeria": "Algérie", "tunisia": "<PERSON><PERSON><PERSON>", "egypt": "Égypte", "saudi": "<PERSON><PERSON>", "uae": "Émirats Arabes Unis", "russia": "<PERSON><PERSON>", "china": "<PERSON>e", "japan": "Japon", "korea": "Corée du Sud", "india": "Inde", "brazil": "Brésil", "mexico": "Mexique", "argentina": "Argentine", "australia": "Australie", "newzealand": "Nouvelle-Zélande", "southafrica": "Afrique du Sud", "nigeria": "Nigeria", "kenya": "Kenya", "turkey": "<PERSON><PERSON><PERSON><PERSON>", "iran": "Iran", "pakistan": "Pakistan", "bangladesh": "Bangladesh", "vietnam": "Vietnam", "thailand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "singapore": "Singapour", "malaysia": "Malaisie", "indonesia": "Indonésie", "philippines": "Philippines", "belgium": "Belgique", "netherlands": "Pays-Bas", "switzerland": "Suisse", "austria": "<PERSON><PERSON><PERSON>", "sweden": "<PERSON><PERSON><PERSON>", "norway": "Norvège", "denmark": "Danemark", "finland": "<PERSON><PERSON>", "poland": "Pologne", "czech": "République Tchèque", "hungary": "Hong<PERSON>", "greece": "<PERSON><PERSON><PERSON><PERSON>", "portugal": "Portugal", "ireland": "<PERSON><PERSON><PERSON>", "canada": "Canada"}, "skills": {"javascript": "JavaScript", "python": "Python", "java": "Java", "cpp": "C++", "csharp": "C#", "php": "PHP", "ruby": "<PERSON>", "go": "Go", "rust": "Rust", "swift": "Swift", "kotlin": "<PERSON><PERSON><PERSON>", "scala": "Scala", "r": "R", "matlab": "MATLAB", "perl": "<PERSON><PERSON>", "react": "React", "vuejs": "Vue.js", "angular": "Angular", "htmlcss": "HTML/CSS", "sass": "Sass", "less": "Less", "bootstrap": "Bootstrap", "tailwind": "Tailwind CSS", "jquery": "j<PERSON><PERSON><PERSON>", "webpack": "Webpack", "vite": "Vite", "nodejs": "Node.js", "expressjs": "Express.js", "django": "Django", "flask": "Flask", "springboot": "Spring Boot", "laravel": "<PERSON><PERSON>", "rails": "Ruby on Rails", "aspnet": "ASP.NET", "fastapi": "FastAPI", "mysql": "MySQL", "postgresql": "PostgreSQL", "mongodb": "MongoDB", "redis": "Redis", "sqlite": "SQLite", "oracle": "Oracle", "sqlserver": "SQL Server", "cassandra": "<PERSON>", "dynamodb": "DynamoDB", "firebase": "Firebase", "aws": "AWS", "azure": "Azure", "googlecloud": "Google Cloud", "docker": "<PERSON>er", "kubernetes": "Kubernetes", "jenkins": "<PERSON>", "gitlabci": "GitLab CI", "githubactions": "GitHub Actions", "terraform": "Terraform", "ansible": "Ansible", "reactnative": "React Native", "flutter": "Flutter", "iosdev": "Développement iOS", "androiddev": "Développement Android", "xamarin": "<PERSON><PERSON><PERSON>", "ionic": "<PERSON><PERSON>", "dataanalysis": "<PERSON><PERSON><PERSON>", "machinelearning": "Apprentissage automatique", "deeplearning": "Apprentissage profond", "ai": "IA", "tensorflow": "TensorFlow", "pytorch": "PyTorch", "pandas": "<PERSON><PERSON>", "numpy": "NumPy", "tableau": "<PERSON><PERSON>", "powerbi": "Power BI", "uiuxdesign": "Design UI/UX", "figma": "Figma", "adobexd": "Adobe XD", "sketch": "Sketch", "photoshop": "Photoshop", "illustrator": "Illustrator", "indesign": "InDesign", "aftereffects": "After Effects", "blender": "<PERSON><PERSON>der", "digitalmarketing": "Marketing numérique", "seo": "SEO", "sem": "SEM", "socialmedia": "Marketing des réseaux sociaux", "contentmarketing": "Marketing de contenu", "emailmarketing": "Email marketing", "googleanalytics": "Google Analytics", "facebookads": "Facebook Ads", "projectmanagement": "Gestion de projet", "agile": "Agile", "scrum": "Scrum", "kanban": "Ka<PERSON><PERSON>", "jira": "<PERSON><PERSON>", "trello": "Trello", "asana": "<PERSON><PERSON>", "mondaycom": "Monday.com", "slack": "<PERSON><PERSON>ck", "git": "Git", "linux": "Linux", "windowsserver": "Windows Server", "cybersecurity": "Cybersécurité", "blockchain": "Blockchain", "iot": "IoT", "apidev": "Développement d'API", "microservices": "Microservices", "graphql": "GraphQL", "restapi": "API REST", "leadership": "Leadership", "communication": "Communication", "problemsolving": "Résolution de problèmes", "criticalthinking": "Pensée critique", "teammanagement": "Gestion d'équipe", "publicspeaking": "Prise de parole en public", "negotiation": "Négociation", "english": "<PERSON><PERSON><PERSON>", "french": "Français", "spanish": "Espagnol", "german": "Allemand", "arabic": "<PERSON><PERSON>", "chinese": "<PERSON><PERSON>", "japanese": "Japonais", "portuguese": "Portugais", "italian": "Italien", "russian": "<PERSON><PERSON>", "ecommerce": "E-commerce", "fintech": "FinTech", "healthtech": "HealthTech", "edtech": "EdTech", "gaming": "<PERSON><PERSON> vidéo", "automotive": "Automobile", "realestate": "Immob<PERSON><PERSON>", "logistics": "Logistique", "retail": "Commerce de détail", "contentwriting": "Rédaction de contenu", "copywriting": "Copywriting", "technicalwriting": "Rédaction technique", "blogwriting": "Rédaction de blog", "socialcontent": "Contenu réseaux sociaux", "videoediting": "Montage vidéo", "podcasting": "Podcasting"}, "whiteboard": {"title": "Tableau Blanc Interactif", "collaborativeWhiteboard": "Tableau Blanc Collaboratif", "tools": {"pen": "<PERSON><PERSON><PERSON>", "text": "Texte", "table": "<PERSON><PERSON>", "color": "Changer <PERSON>ur", "undo": "Annuler", "redo": "<PERSON><PERSON><PERSON>"}}}