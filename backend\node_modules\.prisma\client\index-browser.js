
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.12.0
 * Query Engine version: 8047c96bbd92db98a2abc7c9323ce77c02c89dbc
 */
Prisma.prismaVersion = {
  client: "6.12.0",
  engine: "8047c96bbd92db98a2abc7c9323ce77c02c89dbc"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.MailScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  resetToken: 'resetToken',
  resetTokenExpiry: 'resetTokenExpiry'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  role: 'role',
  email: 'email',
  password: 'password',
  name: 'name',
  phone: 'phone',
  profilePic: 'profilePic',
  location: 'location',
  skills: 'skills',
  about: 'about',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isVerified: 'isVerified',
  needsVerification: 'needsVerification',
  emailVerified: 'emailVerified',
  emailVerificationCode: 'emailVerificationCode',
  codeExpiryDate: 'codeExpiryDate',
  resetToken: 'resetToken',
  resetTokenExpiry: 'resetTokenExpiry'
};

exports.Prisma.FormateurScalarFieldEnum = {
  id: 'id',
  speciality: 'speciality',
  userId: 'userId'
};

exports.Prisma.EtudiantScalarFieldEnum = {
  id: 'id',
  NameEtablissement: 'NameEtablissement',
  userId: 'userId'
};

exports.Prisma.Createur_De_FormationScalarFieldEnum = {
  id: 'id',
  userId: 'userId'
};

exports.Prisma.AdminScalarFieldEnum = {
  id: 'id',
  userId: 'userId'
};

exports.Prisma.EtablissementScalarFieldEnum = {
  id: 'id',
  userId: 'userId'
};

exports.Prisma.ResetTokenScalarFieldEnum = {
  id: 'id',
  token: 'token',
  userId: 'userId',
  expiryDate: 'expiryDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProgramScalarFieldEnum = {
  id: 'id',
  name: 'name',
  published: 'published',
  createdAt: 'createdAt'
};

exports.Prisma.ModuleScalarFieldEnum = {
  id: 'id',
  name: 'name',
  periodUnit: 'periodUnit',
  duration: 'duration'
};

exports.Prisma.CourseScalarFieldEnum = {
  id: 'id',
  title: 'title'
};

exports.Prisma.ContenuScalarFieldEnum = {
  id: 'id',
  title: 'title',
  fileUrl: 'fileUrl',
  fileType: 'fileType',
  type: 'type',
  published: 'published',
  coursAssocie: 'coursAssocie'
};

exports.Prisma.ProgramModuleScalarFieldEnum = {
  id: 'id',
  programId: 'programId',
  moduleId: 'moduleId'
};

exports.Prisma.ModuleCourseScalarFieldEnum = {
  id: 'id',
  moduleId: 'moduleId',
  courseId: 'courseId'
};

exports.Prisma.CourseContenuScalarFieldEnum = {
  id: 'id',
  courseId: 'courseId',
  contenuId: 'contenuId'
};

exports.Prisma.BuildProgramScalarFieldEnum = {
  id: 'id',
  programId: 'programId',
  startDate: 'startDate',
  endDate: 'endDate',
  imageUrl: 'imageUrl',
  level: 'level',
  createdAt: 'createdAt'
};

exports.Prisma.BuildProgramModuleScalarFieldEnum = {
  id: 'id',
  buildProgramId: 'buildProgramId',
  moduleId: 'moduleId'
};

exports.Prisma.BuildProgramCourseScalarFieldEnum = {
  id: 'id',
  buildProgramModuleId: 'buildProgramModuleId',
  courseId: 'courseId'
};

exports.Prisma.BuildProgramContenuScalarFieldEnum = {
  id: 'id',
  buildProgramCourseId: 'buildProgramCourseId',
  contenuId: 'contenuId'
};

exports.Prisma.QuizScalarFieldEnum = {
  id: 'id',
  contenuId: 'contenuId',
  title: 'title',
  description: 'description',
  timeLimit: 'timeLimit'
};

exports.Prisma.QuestionScalarFieldEnum = {
  id: 'id',
  text: 'text',
  imageUrl: 'imageUrl',
  type: 'type',
  score: 'score',
  negativeMark: 'negativeMark',
  quizId: 'quizId',
  correctText: 'correctText'
};

exports.Prisma.ChoiceScalarFieldEnum = {
  id: 'id',
  text: 'text',
  imageUrl: 'imageUrl',
  isCorrect: 'isCorrect',
  questionId: 'questionId'
};

exports.Prisma.UserAnswerScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  quizId: 'quizId',
  score: 'score',
  submittedAt: 'submittedAt'
};

exports.Prisma.AnswerScalarFieldEnum = {
  id: 'id',
  userAnswerId: 'userAnswerId',
  questionId: 'questionId',
  selectedId: 'selectedId',
  textAnswer: 'textAnswer'
};

exports.Prisma.Session2ScalarFieldEnum = {
  id: 'id',
  name: 'name',
  programId: 'programId',
  startDate: 'startDate',
  endDate: 'endDate',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  status: 'status'
};

exports.Prisma.Session2ModuleScalarFieldEnum = {
  id: 'id',
  session2Id: 'session2Id',
  moduleId: 'moduleId'
};

exports.Prisma.Session2CourseScalarFieldEnum = {
  id: 'id',
  session2ModuleId: 'session2ModuleId',
  courseId: 'courseId'
};

exports.Prisma.Session2ContenuScalarFieldEnum = {
  id: 'id',
  session2CourseId: 'session2CourseId',
  contenuId: 'contenuId'
};

exports.Prisma.SeanceFormateurScalarFieldEnum = {
  id: 'id',
  title: 'title',
  startTime: 'startTime',
  formateurId: 'formateurId',
  session2Id: 'session2Id',
  createdAt: 'createdAt',
  buildProgramId: 'buildProgramId'
};

exports.Prisma.SeanceMediaScalarFieldEnum = {
  id: 'id',
  seanceId: 'seanceId',
  type: 'type',
  fileUrl: 'fileUrl',
  createdAt: 'createdAt'
};

exports.Prisma.ChatMessageScalarFieldEnum = {
  id: 'id',
  seanceId: 'seanceId',
  senderId: 'senderId',
  messageId: 'messageId',
  type: 'type',
  content: 'content',
  createdAt: 'createdAt'
};

exports.Prisma.WhiteboardActionScalarFieldEnum = {
  id: 'id',
  seanceId: 'seanceId',
  type: 'type',
  data: 'data',
  createdAt: 'createdAt',
  createdById: 'createdById',
  seanceMediaId: 'seanceMediaId'
};

exports.Prisma.UserSession2ScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  session2Id: 'session2Id',
  createdAt: 'createdAt'
};

exports.Prisma.Session2ChatMessageScalarFieldEnum = {
  id: 'id',
  session2Id: 'session2Id',
  senderId: 'senderId',
  type: 'type',
  content: 'content',
  createdAt: 'createdAt'
};

exports.Prisma.GeneralChatMessageScalarFieldEnum = {
  id: 'id',
  senderId: 'senderId',
  type: 'type',
  content: 'content',
  createdAt: 'createdAt'
};

exports.Prisma.FeedbackScalarFieldEnum = {
  id: 'id',
  rating: 'rating',
  message: 'message',
  category: 'category',
  type: 'type',
  likes: 'likes',
  dislikes: 'dislikes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.FeedbackResponseScalarFieldEnum = {
  id: 'id',
  feedbackId: 'feedbackId',
  response: 'response',
  createdAt: 'createdAt'
};

exports.Prisma.SeanceFeedbackScalarFieldEnum = {
  id: 'id',
  seanceId: 'seanceId',
  userId: 'userId',
  sessionRating: 'sessionRating',
  contentQuality: 'contentQuality',
  sessionDuration: 'sessionDuration',
  sessionOrganization: 'sessionOrganization',
  objectivesAchieved: 'objectivesAchieved',
  trainerRating: 'trainerRating',
  trainerClarity: 'trainerClarity',
  trainerAvailability: 'trainerAvailability',
  trainerPedagogy: 'trainerPedagogy',
  trainerInteraction: 'trainerInteraction',
  teamRating: 'teamRating',
  teamCollaboration: 'teamCollaboration',
  teamParticipation: 'teamParticipation',
  teamCommunication: 'teamCommunication',
  sessionComments: 'sessionComments',
  trainerComments: 'trainerComments',
  teamComments: 'teamComments',
  suggestions: 'suggestions',
  wouldRecommend: 'wouldRecommend',
  improvementAreas: 'improvementAreas',
  createdAt: 'createdAt'
};

exports.Prisma.FeedbackListScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  seanceId: 'seanceId',
  feedback: 'feedback',
  nom: 'nom',
  email: 'email',
  sessionComments: 'sessionComments',
  trainerComments: 'trainerComments',
  teamComments: 'teamComments',
  suggestions: 'suggestions',
  createdAt: 'createdAt'
};

exports.Prisma.SessionFeedbackScalarFieldEnum = {
  id: 'id',
  sessionId: 'sessionId',
  userId: 'userId',
  rating: 'rating',
  comments: 'comments',
  ratings: 'ratings',
  formData: 'formData',
  createdAt: 'createdAt'
};

exports.Prisma.SessionFeedbackListScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  sessionId: 'sessionId',
  feedback: 'feedback',
  nom: 'nom',
  email: 'email',
  sessionComments: 'sessionComments',
  trainerComments: 'trainerComments',
  teamComments: 'teamComments',
  suggestions: 'suggestions',
  createdAt: 'createdAt'
};

exports.Prisma.ChatMemoryScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  sessionId: 'sessionId',
  userMessage: 'userMessage',
  botResponse: 'botResponse',
  role: 'role',
  content: 'content',
  createdAt: 'createdAt'
};

exports.Prisma.FeedbackFormateurScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  studentName: 'studentName',
  emoji: 'emoji',
  emojiLabel: 'emojiLabel',
  commentaire: 'commentaire',
  createdAt: 'createdAt',
  seanceId: 'seanceId',
  formateurId: 'formateurId',
  userId: 'userId'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.Role = exports.$Enums.Role = {
  Etudiant: 'Etudiant',
  Formateur: 'Formateur',
  Admin: 'Admin',
  CreateurDeFormation: 'CreateurDeFormation',
  Etablissement: 'Etablissement'
};

exports.PeriodUnit = exports.$Enums.PeriodUnit = {
  Day: 'Day',
  Week: 'Week',
  Month: 'Month'
};

exports.FileType = exports.$Enums.FileType = {
  PDF: 'PDF',
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  WORD: 'WORD',
  EXCEL: 'EXCEL',
  PPT: 'PPT'
};

exports.ContenuType = exports.$Enums.ContenuType = {
  Cours: 'Cours',
  Exercice: 'Exercice',
  Quiz: 'Quiz'
};

exports.QuestionType = exports.$Enums.QuestionType = {
  MCQ: 'MCQ',
  TRUE_FALSE: 'TRUE_FALSE',
  FILL_BLANK: 'FILL_BLANK',
  IMAGE_CHOICE: 'IMAGE_CHOICE'
};

exports.Session2Status = exports.$Enums.Session2Status = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  COMPLETED: 'COMPLETED',
  ARCHIVED: 'ARCHIVED'
};

exports.Prisma.ModelName = {
  Mail: 'Mail',
  User: 'User',
  Formateur: 'Formateur',
  Etudiant: 'Etudiant',
  Createur_De_Formation: 'Createur_De_Formation',
  Admin: 'Admin',
  Etablissement: 'Etablissement',
  ResetToken: 'ResetToken',
  Program: 'Program',
  Module: 'Module',
  Course: 'Course',
  Contenu: 'Contenu',
  ProgramModule: 'ProgramModule',
  ModuleCourse: 'ModuleCourse',
  CourseContenu: 'CourseContenu',
  buildProgram: 'buildProgram',
  buildProgramModule: 'buildProgramModule',
  buildProgramCourse: 'buildProgramCourse',
  buildProgramContenu: 'buildProgramContenu',
  Quiz: 'Quiz',
  Question: 'Question',
  Choice: 'Choice',
  UserAnswer: 'UserAnswer',
  Answer: 'Answer',
  Session2: 'Session2',
  Session2Module: 'Session2Module',
  Session2Course: 'Session2Course',
  Session2Contenu: 'Session2Contenu',
  SeanceFormateur: 'SeanceFormateur',
  SeanceMedia: 'SeanceMedia',
  ChatMessage: 'ChatMessage',
  WhiteboardAction: 'WhiteboardAction',
  UserSession2: 'UserSession2',
  Session2ChatMessage: 'Session2ChatMessage',
  GeneralChatMessage: 'GeneralChatMessage',
  Feedback: 'Feedback',
  FeedbackResponse: 'FeedbackResponse',
  SeanceFeedback: 'SeanceFeedback',
  FeedbackList: 'FeedbackList',
  SessionFeedback: 'SessionFeedback',
  SessionFeedbackList: 'SessionFeedbackList',
  ChatMemory: 'ChatMemory',
  FeedbackFormateur: 'FeedbackFormateur'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
