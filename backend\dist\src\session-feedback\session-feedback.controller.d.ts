import { SessionFeedbackService } from './session-feedback.service';
import { CreateSessionFeedbackDto } from './dto/create-session-feedback.dto';
export declare class SessionFeedbackController {
    private readonly sessionFeedbackService;
    constructor(sessionFeedbackService: SessionFeedbackService);
    getSessionFeedbackList(sessionId: string): Promise<{
        studentName: string;
        studentEmail: string;
        coordinates: null;
        user: {
            id: number;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            password: string;
            name: string | null;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string[];
            about: string | null;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            isVerified: boolean;
            needsVerification: boolean;
            emailVerified: Date | null;
            emailVerificationCode: string | null;
            codeExpiryDate: Date | null;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
        } | null;
        id: number;
        createdAt: Date;
        userId: number | null;
        rating: number;
        sessionId: number;
        ratings: string | null;
        comments: string | null;
        formData: string | null;
    }[]>;
    getSessionFeedbackListV2(sessionId: string): Promise<{
        id: number;
        userId: number;
        studentName: string | null;
        studentEmail: string | null;
        fullFeedback: string;
        averageRating: number | null;
        emoji: string;
    }[]>;
    getStudentFeedbacks(sessionId: string, userId: string): Promise<{
        id: number;
        sessionId: number;
        userId: number;
        rating: number | null;
        comments: string;
        feedback: string;
        sessionComments: string | null;
        trainerComments: string | null;
        teamComments: string | null;
        suggestions: string | null;
        ratings: null;
        formData: null;
        createdAt: Date;
        studentName: string;
        studentEmail: string;
        user: {
            id: number;
            role: import(".prisma/client").$Enums.Role;
            email: string;
            password: string;
            name: string | null;
            phone: string | null;
            profilePic: string | null;
            location: string | null;
            skills: string[];
            about: string | null;
            isActive: boolean;
            createdAt: Date;
            updatedAt: Date;
            isVerified: boolean;
            needsVerification: boolean;
            emailVerified: Date | null;
            emailVerificationCode: string | null;
            codeExpiryDate: Date | null;
            resetToken: string | null;
            resetTokenExpiry: Date | null;
        };
    }[]>;
    createSessionFeedback(createSessionFeedbackDto: CreateSessionFeedbackDto): Promise<{
        message: string;
        averageRating: number;
    }>;
}
