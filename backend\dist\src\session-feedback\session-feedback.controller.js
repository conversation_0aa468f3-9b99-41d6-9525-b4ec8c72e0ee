"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionFeedbackController = void 0;
const common_1 = require("@nestjs/common");
const session_feedback_service_1 = require("./session-feedback.service");
const create_session_feedback_dto_1 = require("./dto/create-session-feedback.dto");
let SessionFeedbackController = class SessionFeedbackController {
    sessionFeedbackService;
    constructor(sessionFeedbackService) {
        this.sessionFeedbackService = sessionFeedbackService;
    }
    async getSessionFeedbackList(sessionId) {
        if (!sessionId) {
            throw new common_1.BadRequestException('sessionId is required');
        }
        return this.sessionFeedbackService.getSessionFeedbacks(+sessionId);
    }
    async getSessionFeedbackListV2(sessionId) {
        if (!sessionId) {
            throw new common_1.BadRequestException('sessionId is required');
        }
        return this.sessionFeedbackService.getSessionFeedbackList(+sessionId);
    }
    async getStudentFeedbacks(sessionId, userId) {
        if (!sessionId || !userId) {
            throw new common_1.BadRequestException('sessionId and userId are required');
        }
        return this.sessionFeedbackService.getStudentFeedbacks(+sessionId, +userId);
    }
    async createSessionFeedback(createSessionFeedbackDto) {
        return this.sessionFeedbackService.create(createSessionFeedbackDto);
    }
};
exports.SessionFeedbackController = SessionFeedbackController;
__decorate([
    (0, common_1.Get)('feedbacklist/:sessionId'),
    __param(0, (0, common_1.Param)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SessionFeedbackController.prototype, "getSessionFeedbackList", null);
__decorate([
    (0, common_1.Get)('list/:sessionId'),
    __param(0, (0, common_1.Param)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SessionFeedbackController.prototype, "getSessionFeedbackListV2", null);
__decorate([
    (0, common_1.Get)(':sessionId/student/:userId'),
    __param(0, (0, common_1.Param)('sessionId')),
    __param(1, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], SessionFeedbackController.prototype, "getStudentFeedbacks", null);
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ whitelist: true, transform: true })),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_session_feedback_dto_1.CreateSessionFeedbackDto]),
    __metadata("design:returntype", Promise)
], SessionFeedbackController.prototype, "createSessionFeedback", null);
exports.SessionFeedbackController = SessionFeedbackController = __decorate([
    (0, common_1.Controller)('feedback/session'),
    __metadata("design:paramtypes", [session_feedback_service_1.SessionFeedbackService])
], SessionFeedbackController);
//# sourceMappingURL=session-feedback.controller.js.map