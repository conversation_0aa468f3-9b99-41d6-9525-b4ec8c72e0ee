import React, { useState } from 'react';
import { Button, Box, Typography, Alert } from '@mui/material';
import AddSessionFeedback from './pages/users/views/AddSessionFeedback';

const TestFeedback = () => {
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState('');

  // Simuler un utilisateur connecté
  React.useEffect(() => {
    const mockUser = {
      id: 2,
      email: "<EMAIL>",
      name: "Test User",
      role: "Etudiant"
    };
    localStorage.setItem("user", JSON.stringify(mockUser));
  }, []);

  const mockSession = {
    id: 1,
    title: "Formation React Avancé",
    description: "Session de formation sur React et ses concepts avancés"
  };

  const handleOpen = () => {
    setMessage('');
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setMessage('Formulaire fermé');
  };

  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Test du Formulaire d'Évaluation
      </Typography>
      
      {message && (
        <Alert severity="info" sx={{ mb: 2 }}>
          {message}
        </Alert>
      )}
      
      <Button 
        variant="contained" 
        onClick={handleOpen}
        size="large"
        sx={{ mb: 2 }}
      >
        Ouvrir le formulaire d'évaluation
      </Button>
      
      <Typography variant="body2" color="text.secondary">
        Session de test: {mockSession.title}
      </Typography>
      
      <AddSessionFeedback
        open={open}
        onClose={handleClose}
        session={mockSession}
      />
    </Box>
  );
};

export default TestFeedback;
