import React, { useState } from 'react';
import { Button, Box } from '@mui/material';
import AddSessionFeedback from './pages/users/views/AddSessionFeedback';

const TestFeedbackDialog = () => {
  const [open, setOpen] = useState(false);

  const mockSession = {
    id: 1,
    title: "Formation React Avancé",
    description: "Session de formation sur React et ses concepts avancés"
  };

  return (
    <Box sx={{ p: 3 }}>
      <Button 
        variant="contained" 
        onClick={() => setOpen(true)}
        size="large"
      >
        Ouvrir le formulaire d'évaluation
      </Button>
      
      <AddSessionFeedback
        open={open}
        onClose={() => setOpen(false)}
        session={mockSession}
      />
    </Box>
  );
};

export default TestFeedbackDialog;
