import { SeanceFeedbackService } from './seance-feedback.service';
export declare class SeanceFeedbackController {
    private readonly seanceFeedbackService;
    constructor(seanceFeedbackService: SeanceFeedbackService);
    createSeanceFeedback(dto: any): Promise<{
        message: string;
    }>;
    getFeedbackList(seanceId: string): Promise<{
        id: number;
        userId: number;
        studentName: string | null;
        studentEmail: string | null;
        fullFeedback: string;
        averageRating: number | null;
    }[]>;
    getFeedbackDetails(seanceId: string, userId: string): Promise<{
        id: null;
        seanceId: number;
        userId: number;
        studentName: string;
        studentEmail: string;
        sessionRating: null;
        contentQuality: null;
        sessionDuration: null;
        sessionOrganization: null;
        objectivesAchieved: null;
        trainerRating: null;
        trainerClarity: null;
        trainerAvailability: null;
        trainerPedagogy: null;
        trainerInteraction: null;
        teamRating: null;
        teamCollaboration: null;
        teamParticipation: null;
        teamCommunication: null;
        sessionComments: null;
        trainerComments: null;
        teamComments: null;
        suggestions: null;
        wouldRecommend: null;
        improvementAreas: null;
        createdAt: null;
    } | {
        id: number;
        seanceId: number;
        userId: number | null;
        studentName: string;
        studentEmail: string;
        sessionRating: number;
        contentQuality: number;
        sessionDuration: string;
        sessionOrganization: number;
        objectivesAchieved: number;
        trainerRating: number;
        trainerClarity: number;
        trainerAvailability: number;
        trainerPedagogy: number;
        trainerInteraction: number;
        teamRating: number;
        teamCollaboration: number;
        teamParticipation: number;
        teamCommunication: number;
        sessionComments: string | null;
        trainerComments: string | null;
        teamComments: string | null;
        suggestions: string | null;
        wouldRecommend: string | null;
        improvementAreas: string | null;
        createdAt: Date;
    }>;
    cleanupOldFeedbackList(seanceId: string): Promise<{
        deletedCount: number;
    }>;
    cleanupFeedbackList(seanceId: string, emailsToKeep: string[]): Promise<{
        deletedCount: number;
    }>;
}
