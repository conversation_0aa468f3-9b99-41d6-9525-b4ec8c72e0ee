"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeanceFeedbackService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
let SeanceFeedbackService = class SeanceFeedbackService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createSeanceFeedback(dto) {
        const { seanceId, userId } = dto;
        if (!userId)
            throw new common_1.BadRequestException('userId requis');
        if (!seanceId)
            throw new common_1.BadRequestException('seanceId requis');
        const user = await this.prisma.user.findUnique({ where: { id: userId } });
        if (!user)
            throw new common_1.NotFoundException(`Utilisateur ${userId} introuvable`);
        const feedbackMessage = dto.feedback || this.generateFeedbackMessage(dto);
        const existing = await this.prisma.seanceFeedback.findFirst({ where: { seanceId, userId }, orderBy: { createdAt: 'desc' } });
        if (existing) {
            await this.prisma.seanceFeedback.update({
                where: { id: existing.id },
                data: { ...dto },
            });
            await this.prisma.feedbackList.updateMany({
                where: { seanceId, userId },
                data: {
                    feedback: feedbackMessage,
                    nom: user.name ?? '',
                    email: user.email,
                    sessionComments: dto.sessionComments,
                    trainerComments: dto.trainerComments,
                    teamComments: dto.teamComments,
                    suggestions: dto.suggestions,
                },
            });
        }
        else {
            await this.prisma.$transaction([
                this.prisma.seanceFeedback.create({ data: { ...dto, userId } }),
                this.prisma.feedbackList.create({
                    data: {
                        seanceId,
                        userId,
                        feedback: feedbackMessage,
                        nom: user.name ?? '',
                        email: user.email,
                        sessionComments: dto.sessionComments,
                        trainerComments: dto.trainerComments,
                        teamComments: dto.teamComments,
                        suggestions: dto.suggestions,
                    },
                }),
            ]);
        }
        await this.cleanupOldFeedbacks(seanceId);
        return { message: 'Feedback enregistré avec succès' };
    }
    generateFeedbackMessage(dto) {
        const parts = [];
        const ratings = [
            { name: 'Session', value: dto.sessionRating },
            { name: 'Qualité du contenu', value: dto.contentQuality },
            { name: 'Organisation', value: dto.sessionOrganization },
            { name: 'Objectifs atteints', value: dto.objectivesAchieved },
            { name: 'Formateur', value: dto.trainerRating },
            { name: 'Clarté du formateur', value: dto.trainerClarity },
            { name: 'Disponibilité', value: dto.trainerAvailability },
            { name: 'Pédagogie', value: dto.trainerPedagogy },
            { name: 'Interaction', value: dto.trainerInteraction },
            { name: 'Équipe', value: dto.teamRating },
            { name: 'Collaboration', value: dto.teamCollaboration },
            { name: 'Participation', value: dto.teamParticipation },
            { name: 'Communication', value: dto.teamCommunication },
        ];
        const valid = ratings.filter(r => r.value !== null && r.value !== undefined);
        if (valid.length > 0) {
            const avg = valid.reduce((sum, r) => sum + r.value, 0) / valid.length;
            parts.push(`Note moyenne: ${avg.toFixed(1)}/5`);
        }
        if (dto.sessionComments)
            parts.push(`Commentaires sur la session: ${dto.sessionComments}`);
        if (dto.trainerComments)
            parts.push(`Commentaires sur le formateur: ${dto.trainerComments}`);
        if (dto.teamComments)
            parts.push(`Commentaires sur l'équipe: ${dto.teamComments}`);
        if (dto.suggestions)
            parts.push(`Suggestions: ${dto.suggestions}`);
        if (dto.improvementAreas)
            parts.push(`Zones d'amélioration: ${dto.improvementAreas}`);
        if (dto.wouldRecommend)
            parts.push(`Recommanderait: ${dto.wouldRecommend}`);
        return parts.join('\n\n') || 'Feedback de séance soumis';
    }
    async getFeedbackList(seanceId) {
        const feedbacks = await this.prisma.feedbackList.findMany({
            where: { seanceId },
            orderBy: { createdAt: 'desc' },
        });
        const unique = new Map();
        feedbacks.forEach(fb => {
            if (fb.email && !unique.has(fb.email))
                unique.set(fb.email, fb);
        });
        const results = await Promise.all([...unique.values()].map(async (fb) => {
            const seanceFeedback = await this.prisma.seanceFeedback.findFirst({
                where: { seanceId: fb.seanceId, userId: fb.userId },
                orderBy: { createdAt: 'desc' }
            });
            const ratings = [
                seanceFeedback?.sessionRating,
                seanceFeedback?.contentQuality,
                seanceFeedback?.sessionOrganization,
                seanceFeedback?.objectivesAchieved,
                seanceFeedback?.trainerRating,
                seanceFeedback?.trainerClarity,
                seanceFeedback?.trainerAvailability,
                seanceFeedback?.trainerPedagogy,
                seanceFeedback?.trainerInteraction,
                seanceFeedback?.teamRating,
                seanceFeedback?.teamCollaboration,
                seanceFeedback?.teamParticipation,
                seanceFeedback?.teamCommunication,
            ].filter(r => typeof r === 'number');
            const averageRating = ratings.length ? +(ratings.reduce((a, b) => a + b, 0) / ratings.length).toFixed(2) : null;
            return {
                id: fb.id,
                studentName: fb.nom,
                studentEmail: fb.email,
                fullFeedback: fb.feedback,
                averageRating,
            };
        }));
        return results;
    }
    async getFeedbackDetails(seanceId, userId) {
        const seanceFeedback = await this.prisma.seanceFeedback.findFirst({
            where: { seanceId, userId },
            orderBy: { createdAt: 'desc' },
            include: { user: true }
        });
        if (!seanceFeedback) {
            throw new Error('Feedback not found');
        }
        return {
            id: seanceFeedback.id,
            seanceId: seanceFeedback.seanceId,
            userId: seanceFeedback.userId,
            studentName: seanceFeedback.user?.name || '',
            studentEmail: seanceFeedback.user?.email || '',
            sessionRating: seanceFeedback.sessionRating,
            contentQuality: seanceFeedback.contentQuality,
            sessionDuration: seanceFeedback.sessionDuration,
            sessionOrganization: seanceFeedback.sessionOrganization,
            objectivesAchieved: seanceFeedback.objectivesAchieved,
            trainerRating: seanceFeedback.trainerRating,
            trainerClarity: seanceFeedback.trainerClarity,
            trainerAvailability: seanceFeedback.trainerAvailability,
            trainerPedagogy: seanceFeedback.trainerPedagogy,
            trainerInteraction: seanceFeedback.trainerInteraction,
            teamRating: seanceFeedback.teamRating,
            teamCollaboration: seanceFeedback.teamCollaboration,
            teamParticipation: seanceFeedback.teamParticipation,
            teamCommunication: seanceFeedback.teamCommunication,
            sessionComments: seanceFeedback.sessionComments,
            trainerComments: seanceFeedback.trainerComments,
            teamComments: seanceFeedback.teamComments,
            suggestions: seanceFeedback.suggestions,
            wouldRecommend: seanceFeedback.wouldRecommend,
            improvementAreas: seanceFeedback.improvementAreas,
            createdAt: seanceFeedback.createdAt,
        };
    }
    async cleanupOldFeedbacks(seanceId) {
        const all = await this.prisma.feedbackList.findMany({
            where: { seanceId },
            orderBy: { createdAt: 'desc' },
        });
        const latestMap = new Map();
        all.forEach(fb => {
            if (!latestMap.has(fb.userId))
                latestMap.set(fb.userId, fb.id);
        });
        const idsToDelete = all.filter(fb => latestMap.get(fb.userId) !== fb.id).map(fb => fb.id);
        if (!idsToDelete.length)
            return { deletedCount: 0 };
        const deleteResult = await this.prisma.feedbackList.deleteMany({ where: { id: { in: idsToDelete } } });
        return { deletedCount: deleteResult.count };
    }
    async deleteFeedbackListNotInEmails(seanceId, emailsToKeep) {
        if (!emailsToKeep.length) {
            const deleteResult = await this.prisma.feedbackList.deleteMany({ where: { seanceId } });
            return { deletedCount: deleteResult.count };
        }
        const deleteResult = await this.prisma.feedbackList.deleteMany({
            where: {
                seanceId,
                email: { notIn: emailsToKeep },
            },
        });
        return { deletedCount: deleteResult.count };
    }
};
exports.SeanceFeedbackService = SeanceFeedbackService;
exports.SeanceFeedbackService = SeanceFeedbackService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], SeanceFeedbackService);
//# sourceMappingURL=seance-feedback.service.js.map