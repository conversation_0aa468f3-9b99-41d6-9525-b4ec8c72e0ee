{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\TestAverageRating.js\";\nimport React from 'react';\nimport { Box, Typography, Card, CardContent, Grid } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestAverageRating = () => {\n  // Fonction de test pour calculer et formater les moyennes\n  const testAverageCalculation = (ratings, expectedAvg, expectedDisplay) => {\n    // Simuler le calcul comme dans le code réel\n    const validRatings = Object.values(ratings).filter(rating => typeof rating === 'number' && rating >= 1 && rating <= 5);\n    let finalRating = 0;\n    if (validRatings.length > 0) {\n      const sum = validRatings.reduce((acc, rating) => acc + rating, 0);\n      finalRating = sum / validRatings.length;\n    }\n\n    // Formater comme dans le code réel\n    const formatRating = rating => {\n      const rounded = Math.round(rating * 10) / 10;\n      return rounded % 1 === 0 ? rounded.toString() : rounded.toFixed(1).replace('.', ',');\n    };\n    const formattedRating = formatRating(finalRating);\n\n    // Déterminer emoji et couleur\n    let emoji, label, color;\n    if (finalRating >= 4.5) {\n      emoji = '🤩';\n      label = 'Excellent';\n      color = '#4caf50';\n    } else if (finalRating >= 3.5) {\n      emoji = '😊';\n      label = 'Bon';\n      color = '#8bc34a';\n    } else if (finalRating >= 2.5) {\n      emoji = '🙂';\n      label = 'Moyen';\n      color = '#ff9800';\n    } else if (finalRating >= 1.5) {\n      emoji = '😐';\n      label = 'Mauvais';\n      color = '#ff5722';\n    } else {\n      emoji = '😞';\n      label = 'Très mauvais';\n      color = '#f44336';\n    }\n    return {\n      ratings: validRatings,\n      calculatedAvg: finalRating,\n      expectedAvg,\n      formattedDisplay: `${formattedRating}/5`,\n      expectedDisplay,\n      emoji,\n      label,\n      color,\n      isCorrect: Math.abs(finalRating - expectedAvg) < 0.01 && formattedDisplay === expectedDisplay\n    };\n  };\n\n  // Cas de test\n  const testCases = [{\n    name: \"Moyenne entière 4.0\",\n    ratings: {\n      overallRating: 4,\n      contentRelevance: 4,\n      learningObjectives: 4,\n      sessionStructure: 4\n    },\n    expectedAvg: 4.0,\n    expectedDisplay: \"4/5\"\n  }, {\n    name: \"Moyenne décimale 4.3\",\n    ratings: {\n      overallRating: 4,\n      contentRelevance: 5,\n      learningObjectives: 4,\n      sessionStructure: 4\n    },\n    expectedAvg: 4.25,\n    expectedDisplay: \"4,3/5\"\n  }, {\n    name: \"Moyenne décimale 3.7\",\n    ratings: {\n      overallRating: 4,\n      contentRelevance: 3,\n      learningObjectives: 4,\n      sessionStructure: 4\n    },\n    expectedAvg: 3.75,\n    expectedDisplay: \"3,8/5\"\n  }, {\n    name: \"Moyenne décimale 3.5\",\n    ratings: {\n      overallRating: 3,\n      contentRelevance: 4,\n      learningObjectives: 3,\n      sessionStructure: 4\n    },\n    expectedAvg: 3.5,\n    expectedDisplay: \"3,5/5\"\n  }, {\n    name: \"Moyenne décimale 2.3\",\n    ratings: {\n      overallRating: 2,\n      contentRelevance: 3,\n      learningObjectives: 2,\n      sessionStructure: 2\n    },\n    expectedAvg: 2.25,\n    expectedDisplay: \"2,3/5\"\n  }, {\n    name: \"Moyenne haute 4.8\",\n    ratings: {\n      overallRating: 5,\n      contentRelevance: 5,\n      learningObjectives: 4,\n      sessionStructure: 5\n    },\n    expectedAvg: 4.75,\n    expectedDisplay: \"4,8/5\"\n  }];\n  const results = testCases.map(testCase => testAverageCalculation(testCase.ratings, testCase.expectedAvg, testCase.expectedDisplay));\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3,\n      maxWidth: 1200,\n      mx: 'auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"\\uD83E\\uDDEA Test des Moyennes D\\xE9cimales - Average Rating\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      sx: {\n        mb: 3\n      },\n      children: \"V\\xE9rification que les moyennes d\\xE9cimales s'affichent correctement dans la colonne Average Rating.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: testCases.map((testCase, index) => {\n        const result = results[index];\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              border: result.isCorrect ? '2px solid #4caf50' : '2px solid #f44336',\n              bgcolor: result.isCorrect ? '#f1f8e9' : '#ffebee'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: [result.isCorrect ? '✅' : '❌', \" \", testCase.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Notes individuelles:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 21\n                }, this), \" \", result.ratings.join(', ')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Moyenne calcul\\xE9e:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 21\n                }, this), \" \", result.calculatedAvg.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Affichage:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: '1.2rem',\n                    marginLeft: '8px'\n                  },\n                  children: [result.emoji, \" \", result.formattedDisplay]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: result.color,\n                    marginLeft: '8px',\n                    fontWeight: 'bold'\n                  },\n                  children: result.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Attendu:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), \" \", testCase.expectedDisplay]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), !result.isCorrect && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"error\",\n                children: \"\\u26A0\\uFE0F Diff\\xE9rence d\\xE9tect\\xE9e !\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 4,\n        p: 2,\n        bgcolor: '#e3f2fd',\n        borderRadius: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"\\uD83D\\uDCCA R\\xE9sum\\xE9 des tests\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: [\"Tests r\\xE9ussis: \", results.filter(r => r.isCorrect).length, \" / \", results.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), results.every(r => r.isCorrect) ? /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"success.main\",\n        sx: {\n          fontWeight: 'bold'\n        },\n        children: \"\\uD83C\\uDF89 Tous les tests sont r\\xE9ussis ! Les moyennes d\\xE9cimales s'affichent correctement.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        color: \"error.main\",\n        sx: {\n          fontWeight: 'bold'\n        },\n        children: \"\\u26A0\\uFE0F Certains tests ont \\xE9chou\\xE9. V\\xE9rifiez le code de formatage.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this);\n};\n_c = TestAverageRating;\nexport default TestAverageRating;\nvar _c;\n$RefreshReg$(_c, \"TestAverageRating\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "jsxDEV", "_jsxDEV", "TestAverageRating", "testAverageCalculation", "ratings", "expectedAvg", "expectedDisplay", "validRatings", "Object", "values", "filter", "rating", "finalRating", "length", "sum", "reduce", "acc", "formatRating", "rounded", "Math", "round", "toString", "toFixed", "replace", "formattedRating", "emoji", "label", "color", "calculatedAvg", "formattedDisplay", "isCorrect", "abs", "testCases", "name", "overallRating", "contentRelevance", "learningObjectives", "sessionStructure", "results", "map", "testCase", "sx", "p", "max<PERSON><PERSON><PERSON>", "mx", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "container", "spacing", "index", "result", "item", "xs", "md", "border", "bgcolor", "join", "style", "fontSize", "marginLeft", "fontWeight", "mt", "borderRadius", "r", "every", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/TestAverageRating.js"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Card, CardContent, Grid } from '@mui/material';\n\nconst TestAverageRating = () => {\n  // Fonction de test pour calculer et formater les moyennes\n  const testAverageCalculation = (ratings, expectedAvg, expectedDisplay) => {\n    // Simuler le calcul comme dans le code réel\n    const validRatings = Object.values(ratings)\n      .filter(rating => typeof rating === 'number' && rating >= 1 && rating <= 5);\n    \n    let finalRating = 0;\n    if (validRatings.length > 0) {\n      const sum = validRatings.reduce((acc, rating) => acc + rating, 0);\n      finalRating = sum / validRatings.length;\n    }\n    \n    // Formater comme dans le code réel\n    const formatRating = (rating) => {\n      const rounded = Math.round(rating * 10) / 10;\n      return rounded % 1 === 0 ? rounded.toString() : rounded.toFixed(1).replace('.', ',');\n    };\n    \n    const formattedRating = formatRating(finalRating);\n    \n    // Déterminer emoji et couleur\n    let emoji, label, color;\n    if (finalRating >= 4.5) {\n      emoji = '🤩'; label = 'Excellent'; color = '#4caf50';\n    } else if (finalRating >= 3.5) {\n      emoji = '😊'; label = 'Bon'; color = '#8bc34a';\n    } else if (finalRating >= 2.5) {\n      emoji = '🙂'; label = 'Moyen'; color = '#ff9800';\n    } else if (finalRating >= 1.5) {\n      emoji = '😐'; label = 'Mauvais'; color = '#ff5722';\n    } else {\n      emoji = '😞'; label = 'Très mauvais'; color = '#f44336';\n    }\n    \n    return {\n      ratings: validRatings,\n      calculatedAvg: finalRating,\n      expectedAvg,\n      formattedDisplay: `${formattedRating}/5`,\n      expectedDisplay,\n      emoji,\n      label,\n      color,\n      isCorrect: Math.abs(finalRating - expectedAvg) < 0.01 && formattedDisplay === expectedDisplay\n    };\n  };\n\n  // Cas de test\n  const testCases = [\n    {\n      name: \"Moyenne entière 4.0\",\n      ratings: { overallRating: 4, contentRelevance: 4, learningObjectives: 4, sessionStructure: 4 },\n      expectedAvg: 4.0,\n      expectedDisplay: \"4/5\"\n    },\n    {\n      name: \"Moyenne décimale 4.3\",\n      ratings: { overallRating: 4, contentRelevance: 5, learningObjectives: 4, sessionStructure: 4 },\n      expectedAvg: 4.25,\n      expectedDisplay: \"4,3/5\"\n    },\n    {\n      name: \"Moyenne décimale 3.7\",\n      ratings: { overallRating: 4, contentRelevance: 3, learningObjectives: 4, sessionStructure: 4 },\n      expectedAvg: 3.75,\n      expectedDisplay: \"3,8/5\"\n    },\n    {\n      name: \"Moyenne décimale 3.5\",\n      ratings: { overallRating: 3, contentRelevance: 4, learningObjectives: 3, sessionStructure: 4 },\n      expectedAvg: 3.5,\n      expectedDisplay: \"3,5/5\"\n    },\n    {\n      name: \"Moyenne décimale 2.3\",\n      ratings: { overallRating: 2, contentRelevance: 3, learningObjectives: 2, sessionStructure: 2 },\n      expectedAvg: 2.25,\n      expectedDisplay: \"2,3/5\"\n    },\n    {\n      name: \"Moyenne haute 4.8\",\n      ratings: { overallRating: 5, contentRelevance: 5, learningObjectives: 4, sessionStructure: 5 },\n      expectedAvg: 4.75,\n      expectedDisplay: \"4,8/5\"\n    }\n  ];\n\n  const results = testCases.map(testCase => testAverageCalculation(\n    testCase.ratings, \n    testCase.expectedAvg, \n    testCase.expectedDisplay\n  ));\n\n  return (\n    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>\n      <Typography variant=\"h4\" gutterBottom>\n        🧪 Test des Moyennes Décimales - Average Rating\n      </Typography>\n      \n      <Typography variant=\"body1\" sx={{ mb: 3 }}>\n        Vérification que les moyennes décimales s'affichent correctement dans la colonne Average Rating.\n      </Typography>\n\n      <Grid container spacing={2}>\n        {testCases.map((testCase, index) => {\n          const result = results[index];\n          return (\n            <Grid item xs={12} md={6} key={index}>\n              <Card sx={{ \n                border: result.isCorrect ? '2px solid #4caf50' : '2px solid #f44336',\n                bgcolor: result.isCorrect ? '#f1f8e9' : '#ffebee'\n              }}>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    {result.isCorrect ? '✅' : '❌'} {testCase.name}\n                  </Typography>\n                  \n                  <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                    <strong>Notes individuelles:</strong> {result.ratings.join(', ')}\n                  </Typography>\n                  \n                  <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                    <strong>Moyenne calculée:</strong> {result.calculatedAvg.toFixed(2)}\n                  </Typography>\n                  \n                  <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                    <strong>Affichage:</strong> \n                    <span style={{ fontSize: '1.2rem', marginLeft: '8px' }}>\n                      {result.emoji} {result.formattedDisplay}\n                    </span>\n                    <span style={{ color: result.color, marginLeft: '8px', fontWeight: 'bold' }}>\n                      {result.label}\n                    </span>\n                  </Typography>\n                  \n                  <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                    <strong>Attendu:</strong> {testCase.expectedDisplay}\n                  </Typography>\n                  \n                  {!result.isCorrect && (\n                    <Typography variant=\"body2\" color=\"error\">\n                      ⚠️ Différence détectée !\n                    </Typography>\n                  )}\n                </CardContent>\n              </Card>\n            </Grid>\n          );\n        })}\n      </Grid>\n\n      <Box sx={{ mt: 4, p: 2, bgcolor: '#e3f2fd', borderRadius: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          📊 Résumé des tests\n        </Typography>\n        <Typography variant=\"body1\">\n          Tests réussis: {results.filter(r => r.isCorrect).length} / {results.length}\n        </Typography>\n        {results.every(r => r.isCorrect) ? (\n          <Typography variant=\"body1\" color=\"success.main\" sx={{ fontWeight: 'bold' }}>\n            🎉 Tous les tests sont réussis ! Les moyennes décimales s'affichent correctement.\n          </Typography>\n        ) : (\n          <Typography variant=\"body1\" color=\"error.main\" sx={{ fontWeight: 'bold' }}>\n            ⚠️ Certains tests ont échoué. Vérifiez le code de formatage.\n          </Typography>\n        )}\n      </Box>\n    </Box>\n  );\n};\n\nexport default TestAverageRating;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAEC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B;EACA,MAAMC,sBAAsB,GAAGA,CAACC,OAAO,EAAEC,WAAW,EAAEC,eAAe,KAAK;IACxE;IACA,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACL,OAAO,CAAC,CACxCM,MAAM,CAACC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,IAAI,CAAC,IAAIA,MAAM,IAAI,CAAC,CAAC;IAE7E,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIL,YAAY,CAACM,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMC,GAAG,GAAGP,YAAY,CAACQ,MAAM,CAAC,CAACC,GAAG,EAAEL,MAAM,KAAKK,GAAG,GAAGL,MAAM,EAAE,CAAC,CAAC;MACjEC,WAAW,GAAGE,GAAG,GAAGP,YAAY,CAACM,MAAM;IACzC;;IAEA;IACA,MAAMI,YAAY,GAAIN,MAAM,IAAK;MAC/B,MAAMO,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACT,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;MAC5C,OAAOO,OAAO,GAAG,CAAC,KAAK,CAAC,GAAGA,OAAO,CAACG,QAAQ,CAAC,CAAC,GAAGH,OAAO,CAACI,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IACtF,CAAC;IAED,MAAMC,eAAe,GAAGP,YAAY,CAACL,WAAW,CAAC;;IAEjD;IACA,IAAIa,KAAK,EAAEC,KAAK,EAAEC,KAAK;IACvB,IAAIf,WAAW,IAAI,GAAG,EAAE;MACtBa,KAAK,GAAG,IAAI;MAAEC,KAAK,GAAG,WAAW;MAAEC,KAAK,GAAG,SAAS;IACtD,CAAC,MAAM,IAAIf,WAAW,IAAI,GAAG,EAAE;MAC7Ba,KAAK,GAAG,IAAI;MAAEC,KAAK,GAAG,KAAK;MAAEC,KAAK,GAAG,SAAS;IAChD,CAAC,MAAM,IAAIf,WAAW,IAAI,GAAG,EAAE;MAC7Ba,KAAK,GAAG,IAAI;MAAEC,KAAK,GAAG,OAAO;MAAEC,KAAK,GAAG,SAAS;IAClD,CAAC,MAAM,IAAIf,WAAW,IAAI,GAAG,EAAE;MAC7Ba,KAAK,GAAG,IAAI;MAAEC,KAAK,GAAG,SAAS;MAAEC,KAAK,GAAG,SAAS;IACpD,CAAC,MAAM;MACLF,KAAK,GAAG,IAAI;MAAEC,KAAK,GAAG,cAAc;MAAEC,KAAK,GAAG,SAAS;IACzD;IAEA,OAAO;MACLvB,OAAO,EAAEG,YAAY;MACrBqB,aAAa,EAAEhB,WAAW;MAC1BP,WAAW;MACXwB,gBAAgB,EAAE,GAAGL,eAAe,IAAI;MACxClB,eAAe;MACfmB,KAAK;MACLC,KAAK;MACLC,KAAK;MACLG,SAAS,EAAEX,IAAI,CAACY,GAAG,CAACnB,WAAW,GAAGP,WAAW,CAAC,GAAG,IAAI,IAAIwB,gBAAgB,KAAKvB;IAChF,CAAC;EACH,CAAC;;EAED;EACA,MAAM0B,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,qBAAqB;IAC3B7B,OAAO,EAAE;MAAE8B,aAAa,EAAE,CAAC;MAAEC,gBAAgB,EAAE,CAAC;MAAEC,kBAAkB,EAAE,CAAC;MAAEC,gBAAgB,EAAE;IAAE,CAAC;IAC9FhC,WAAW,EAAE,GAAG;IAChBC,eAAe,EAAE;EACnB,CAAC,EACD;IACE2B,IAAI,EAAE,sBAAsB;IAC5B7B,OAAO,EAAE;MAAE8B,aAAa,EAAE,CAAC;MAAEC,gBAAgB,EAAE,CAAC;MAAEC,kBAAkB,EAAE,CAAC;MAAEC,gBAAgB,EAAE;IAAE,CAAC;IAC9FhC,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAE;EACnB,CAAC,EACD;IACE2B,IAAI,EAAE,sBAAsB;IAC5B7B,OAAO,EAAE;MAAE8B,aAAa,EAAE,CAAC;MAAEC,gBAAgB,EAAE,CAAC;MAAEC,kBAAkB,EAAE,CAAC;MAAEC,gBAAgB,EAAE;IAAE,CAAC;IAC9FhC,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAE;EACnB,CAAC,EACD;IACE2B,IAAI,EAAE,sBAAsB;IAC5B7B,OAAO,EAAE;MAAE8B,aAAa,EAAE,CAAC;MAAEC,gBAAgB,EAAE,CAAC;MAAEC,kBAAkB,EAAE,CAAC;MAAEC,gBAAgB,EAAE;IAAE,CAAC;IAC9FhC,WAAW,EAAE,GAAG;IAChBC,eAAe,EAAE;EACnB,CAAC,EACD;IACE2B,IAAI,EAAE,sBAAsB;IAC5B7B,OAAO,EAAE;MAAE8B,aAAa,EAAE,CAAC;MAAEC,gBAAgB,EAAE,CAAC;MAAEC,kBAAkB,EAAE,CAAC;MAAEC,gBAAgB,EAAE;IAAE,CAAC;IAC9FhC,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAE;EACnB,CAAC,EACD;IACE2B,IAAI,EAAE,mBAAmB;IACzB7B,OAAO,EAAE;MAAE8B,aAAa,EAAE,CAAC;MAAEC,gBAAgB,EAAE,CAAC;MAAEC,kBAAkB,EAAE,CAAC;MAAEC,gBAAgB,EAAE;IAAE,CAAC;IAC9FhC,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAE;EACnB,CAAC,CACF;EAED,MAAMgC,OAAO,GAAGN,SAAS,CAACO,GAAG,CAACC,QAAQ,IAAIrC,sBAAsB,CAC9DqC,QAAQ,CAACpC,OAAO,EAChBoC,QAAQ,CAACnC,WAAW,EACpBmC,QAAQ,CAAClC,eACX,CAAC,CAAC;EAEF,oBACEL,OAAA,CAACN,GAAG;IAAC8C,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,QAAQ,EAAE,IAAI;MAAEC,EAAE,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC5C5C,OAAA,CAACL,UAAU;MAACkD,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEblD,OAAA,CAACL,UAAU;MAACkD,OAAO,EAAC,OAAO;MAACL,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EAAC;IAE3C;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEblD,OAAA,CAACF,IAAI;MAACsD,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAT,QAAA,EACxBb,SAAS,CAACO,GAAG,CAAC,CAACC,QAAQ,EAAEe,KAAK,KAAK;QAClC,MAAMC,MAAM,GAAGlB,OAAO,CAACiB,KAAK,CAAC;QAC7B,oBACEtD,OAAA,CAACF,IAAI;UAAC0D,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAd,QAAA,eACvB5C,OAAA,CAACJ,IAAI;YAAC4C,EAAE,EAAE;cACRmB,MAAM,EAAEJ,MAAM,CAAC1B,SAAS,GAAG,mBAAmB,GAAG,mBAAmB;cACpE+B,OAAO,EAAEL,MAAM,CAAC1B,SAAS,GAAG,SAAS,GAAG;YAC1C,CAAE;YAAAe,QAAA,eACA5C,OAAA,CAACH,WAAW;cAAA+C,QAAA,gBACV5C,OAAA,CAACL,UAAU;gBAACkD,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAF,QAAA,GAClCW,MAAM,CAAC1B,SAAS,GAAG,GAAG,GAAG,GAAG,EAAC,GAAC,EAACU,QAAQ,CAACP,IAAI;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAEblD,OAAA,CAACL,UAAU;gBAACkD,OAAO,EAAC,OAAO;gBAACL,EAAE,EAAE;kBAAEW,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,gBACxC5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAACpD,OAAO,CAAC0D,IAAI,CAAC,IAAI,CAAC;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eAEblD,OAAA,CAACL,UAAU;gBAACkD,OAAO,EAAC,OAAO;gBAACL,EAAE,EAAE;kBAAEW,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,gBACxC5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAAC5B,aAAa,CAACN,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eAEblD,OAAA,CAACL,UAAU;gBAACkD,OAAO,EAAC,OAAO;gBAACL,EAAE,EAAE;kBAAEW,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,gBACxC5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAU;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3BlD,OAAA;kBAAM8D,KAAK,EAAE;oBAAEC,QAAQ,EAAE,QAAQ;oBAAEC,UAAU,EAAE;kBAAM,CAAE;kBAAApB,QAAA,GACpDW,MAAM,CAAC/B,KAAK,EAAC,GAAC,EAAC+B,MAAM,CAAC3B,gBAAgB;gBAAA;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACPlD,OAAA;kBAAM8D,KAAK,EAAE;oBAAEpC,KAAK,EAAE6B,MAAM,CAAC7B,KAAK;oBAAEsC,UAAU,EAAE,KAAK;oBAAEC,UAAU,EAAE;kBAAO,CAAE;kBAAArB,QAAA,EACzEW,MAAM,CAAC9B;gBAAK;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eAEblD,OAAA,CAACL,UAAU;gBAACkD,OAAO,EAAC,OAAO;gBAACL,EAAE,EAAE;kBAAEW,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,gBACxC5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACX,QAAQ,CAAClC,eAAe;cAAA;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,EAEZ,CAACK,MAAM,CAAC1B,SAAS,iBAChB7B,OAAA,CAACL,UAAU;gBAACkD,OAAO,EAAC,OAAO;gBAACnB,KAAK,EAAC,OAAO;gBAAAkB,QAAA,EAAC;cAE1C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GAtCsBI,KAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuC9B,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPlD,OAAA,CAACN,GAAG;MAAC8C,EAAE,EAAE;QAAE0B,EAAE,EAAE,CAAC;QAAEzB,CAAC,EAAE,CAAC;QAAEmB,OAAO,EAAE,SAAS;QAAEO,YAAY,EAAE;MAAE,CAAE;MAAAvB,QAAA,gBAC5D5C,OAAA,CAACL,UAAU;QAACkD,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACblD,OAAA,CAACL,UAAU;QAACkD,OAAO,EAAC,OAAO;QAAAD,QAAA,GAAC,oBACX,EAACP,OAAO,CAAC5B,MAAM,CAAC2D,CAAC,IAAIA,CAAC,CAACvC,SAAS,CAAC,CAACjB,MAAM,EAAC,KAAG,EAACyB,OAAO,CAACzB,MAAM;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,EACZb,OAAO,CAACgC,KAAK,CAACD,CAAC,IAAIA,CAAC,CAACvC,SAAS,CAAC,gBAC9B7B,OAAA,CAACL,UAAU;QAACkD,OAAO,EAAC,OAAO;QAACnB,KAAK,EAAC,cAAc;QAACc,EAAE,EAAE;UAAEyB,UAAU,EAAE;QAAO,CAAE;QAAArB,QAAA,EAAC;MAE7E;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAEblD,OAAA,CAACL,UAAU;QAACkD,OAAO,EAAC,OAAO;QAACnB,KAAK,EAAC,YAAY;QAACc,EAAE,EAAE;UAAEyB,UAAU,EAAE;QAAO,CAAE;QAAArB,QAAA,EAAC;MAE3E;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACoB,EAAA,GA3KIrE,iBAAiB;AA6KvB,eAAeA,iBAAiB;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}