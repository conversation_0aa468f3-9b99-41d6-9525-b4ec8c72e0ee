{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from \"react\";\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\nimport './i18n';\nimport { useTranslation } from 'react-i18next';\n\n// Pages\nimport LoginPage from \"./pages/LoginPage\";\nimport ForgetPasswordPage from \"./pages/ForgetPasswordPage\";\nimport ResetPasswordPage from \"./pages/ResetPasswordPage\";\nimport ResetSuccessPage from \"./pages/ResetSuccessPage\";\nimport NotFound from \"./pages/NotFoundPage\";\nimport HomePage from \"./pages/HomePage\";\nimport FeedbackPage from \"./pages/FeedbackPage\";\nimport StudentLandingPage from \"./pages/StudentLandingPage\";\nimport StudentProgramPage from \"./pages/StudentProgramPage\";\nimport ProfilePage from \"./pages/ProfilePage\";\nimport EditProfilePage from \"./pages/EditProfilePage\";\n\n// Auth / Main/chatbot containers\nimport Auth from \"./apps/Auth\";\nimport Main from \"./apps/Main\";\n\n// User-related pages\nimport UsersPages from \"./pages/users/UsersPages\";\nimport AddUserView from \"./pages/users/views/AddUserView\";\nimport UserList from \"./pages/users/views/UserList\";\n\n// Test component\nimport TestFeedback from \"./TestFeedback\";\n\n// Program / Module / Course/ quiz / session\nimport ProgramsPage from \"./pages/ProgramsPage\";\nimport AddProgramList from \"./pages/users/views/AddProgramList\";\nimport ModulePage from \"./pages/ModulePage\";\nimport AddModuleView from \"./pages/users/views/AddModuleView\";\nimport CoursesPage from \"./pages/CoursesPage\";\nimport ContenusPage from \"./pages/ContenusPage\";\nimport AddQuizForm from \"./pages/users/views/AddQuizForm\";\nimport PlayQuizPage from \"./pages/users/views/PlayQuizPage\";\nimport BuildProgramView from \"./pages/users/views/BuildProgramView\";\nimport BuildProgramOverviewPage from \"./pages/BuildProgramOverviewPage\";\nimport ModuleList from './pages/users/views/ModuleList';\nimport EditProgramView from \"./pages/users/views/EditProgramView\";\nimport SessionPage from \"./pages/SessionPage\";\nimport EditQuizForm from \"./pages/users/views/EditQuizForm\";\nimport VerifyAccountPage from './pages/VerifyAccountPage';\nimport SeanceFormateurPage from \"./pages/SeanceFormateurPage\";\nimport AddSeanceFormateurView from \"./pages/users/views/AddSeanceFormateurView\";\nimport SeanceFormateurList from \"./pages/users/views/SeanceFormateurList\";\nimport AnimerSeanceView from \"./pages/users/views/AnimerSeanceView\";\nimport SessionDetail from \"./pages/SessionDetail\";\nimport SessionFeedbackList from './pages/users/views/SessionFeedbackList';\nimport JitsiRoom from './components/JitsiRoom';\nimport Chatbot from './components/Chatbot';\n// import TestChatPage from \"./pages/TestChatPage\";\nimport WhiteboardPage from \"./pages/WhiteboardPage\";\n\n//Dashboards\nimport AdminDashboard from \"./pages/users/views/AdminDashboard\";\nimport EtablissementDashboard from \"./pages/users/views/EtablissementDashboard\";\nimport CreateurDashboard from \"./pages/users/views/CreateurDashboard\";\nimport FormateurDashboard from \"./pages/users/views/FormateurDashboard\";\nimport EtudiantDashboard from \"./pages/users/views/EtudiantDashboard\";\n\n// UI\nimport Spinner from \"react-bootstrap/Spinner\";\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport { FaRegMoon } from \"react-icons/fa\";\nimport { GoSun } from \"react-icons/go\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [user, setUser] = useState(null);\n  const [darkMode, setDarkMode] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const loadUserFromStorage = () => {\n    console.log(\"=== LOADING USER FROM STORAGE ===\");\n\n    // Logique simplifiée : vérifier localStorage puis sessionStorage\n    let userStr = localStorage.getItem(\"user\");\n    let storageType = \"localStorage\";\n    if (!userStr) {\n      userStr = sessionStorage.getItem(\"user\");\n      storageType = \"sessionStorage\";\n    }\n    if (!userStr) {\n      console.log(\"❌ No user found in storage\");\n      return null;\n    }\n    try {\n      const user = JSON.parse(userStr);\n      console.log(`✅ User loaded from ${storageType}:`, user.email);\n\n      // Vérifier si l'utilisateur a un rôle, sinon définir \"Etudiant\" par défaut\n      if (!user.role || user.role === \"user\") {\n        user.role = \"Etudiant\";\n      }\n\n      // Cas spécial pour l'utilisateur khalil\n      if (user.email === \"<EMAIL>\" && user.role !== \"Admin\") {\n        user.role = \"Admin\";\n        // Mettre à jour les données utilisateur dans le storage approprié\n        const updatedUserStr = JSON.stringify(user);\n        if (storageType === \"localStorage\") {\n          localStorage.setItem(\"user\", updatedUserStr);\n        } else {\n          sessionStorage.setItem(\"user\", updatedUserStr);\n        }\n      }\n      return user;\n    } catch (err) {\n      console.error(\"❌ Error parsing stored user:\", err);\n      // En cas d'erreur, nettoyer les storages\n      localStorage.removeItem(\"user\");\n      sessionStorage.removeItem(\"user\");\n      return null;\n    }\n  };\n  useEffect(() => {\n    console.log(\"=== APP INITIALIZATION ===\");\n\n    // Charger l'utilisateur depuis le storage\n    const userData = loadUserFromStorage();\n    if (userData) {\n      console.log(\"✅ User loaded from storage:\", userData.email);\n      setUser(userData);\n    } else {\n      console.log(\"❌ No user found in storage\");\n      setUser(null);\n    }\n    setLoading(false);\n    console.log(\"=== APP INITIALIZATION COMPLETED ===\");\n  }, []);\n\n  // Écouter les événements de déconnexion\n  useEffect(() => {\n    const handleStorageChange = e => {\n      console.log(\"Storage change detected:\", e.key, e.newValue);\n\n      // Si les données utilisateur ont été supprimées, mettre à jour l'état\n      if (e.key === 'user' && !e.newValue) {\n        console.log(\"User data removed from storage, logging out...\");\n        setUser(null);\n      }\n    };\n    const handleUserLogout = () => {\n      console.log(\"User logout event received\");\n      setUser(null);\n    };\n\n    // Écouter les changements de localStorage\n    window.addEventListener('storage', handleStorageChange);\n\n    // Écouter l'événement personnalisé de déconnexion\n    window.addEventListener('userLogout', handleUserLogout);\n\n    // Nettoyer les écouteurs d'événements\n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n      window.removeEventListener('userLogout', handleUserLogout);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${darkMode ? \"text-white bg-dark position-fixed h-100 w-100\" : \"\"}`,\n    children: [/*#__PURE__*/_jsxDEV(ToastContainer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-end\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-light d-flex align-items-center\",\n        onClick: () => setDarkMode(!darkMode),\n        children: darkMode ? /*#__PURE__*/_jsxDEV(GoSun, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 23\n        }, this) : /*#__PURE__*/_jsxDEV(FaRegMoon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 35\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), user && /*#__PURE__*/_jsxDEV(Chatbot, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 16\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: \"80vh\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Spinner, {\n        animation: \"border\",\n        variant: \"primary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(BrowserRouter, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(LoginPage, {\n            setUser: setUser\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), user ? /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Main, {\n            setUser: setUser,\n            user: user\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 40\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            index: true,\n            element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 39\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"users\",\n            element: /*#__PURE__*/_jsxDEV(UsersPages, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 46\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              index: true,\n              element: /*#__PURE__*/_jsxDEV(UserList, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"add\",\n              element: /*#__PURE__*/_jsxDEV(AddUserView, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"programs\",\n            element: /*#__PURE__*/_jsxDEV(ProgramsPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"programs/add\",\n            element: /*#__PURE__*/_jsxDEV(AddProgramList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 53\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"module\",\n            element: /*#__PURE__*/_jsxDEV(ModulePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"module/add\",\n            element: /*#__PURE__*/_jsxDEV(AddModuleView, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 51\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"courses/*\",\n            element: /*#__PURE__*/_jsxDEV(CoursesPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"contenus/*\",\n            element: /*#__PURE__*/_jsxDEV(ContenusPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 51\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/programs/overview\",\n            element: /*#__PURE__*/_jsxDEV(BuildProgramOverviewPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quizzes/create/:contenuId\",\n            element: /*#__PURE__*/_jsxDEV(AddQuizForm, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 67\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quizzes/play/:contenuId\",\n            element: /*#__PURE__*/_jsxDEV(PlayQuizPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 65\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/programs/build/:programId\",\n            element: /*#__PURE__*/_jsxDEV(BuildProgramView, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 67\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/programs/overview/:programId\",\n            element: /*#__PURE__*/_jsxDEV(BuildProgramOverviewPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 70\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/modules\",\n            element: /*#__PURE__*/_jsxDEV(ModuleList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/programs/edit/:programId\",\n            element: /*#__PURE__*/_jsxDEV(EditProgramView, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 66\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/sessions\",\n            element: /*#__PURE__*/_jsxDEV(SessionPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/quizzes/edit/:contenuId\",\n            element: /*#__PURE__*/_jsxDEV(EditQuizForm, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 65\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/formateur/seances\",\n            element: /*#__PURE__*/_jsxDEV(SeanceFormateurPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/seances-formateur/add\",\n            element: /*#__PURE__*/_jsxDEV(AddSeanceFormateurView, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 63\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/seances-formateur\",\n            element: /*#__PURE__*/_jsxDEV(SeanceFormateurList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/formateur/seances\",\n            element: /*#__PURE__*/_jsxDEV(SeanceFormateurPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 59\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/sessions/:sessionId/seances\",\n            element: /*#__PURE__*/_jsxDEV(SeanceFormateurPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 69\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/sessions/:sessionId/feedbacklist\",\n            element: /*#__PURE__*/_jsxDEV(SessionFeedbackList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 74\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/formateur/seance/:id\",\n            element: /*#__PURE__*/_jsxDEV(AnimerSeanceView, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 62\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/jitsi\",\n            element: /*#__PURE__*/_jsxDEV(JitsiRoom, {\n              roomName: \"majd-room\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/whiteboard/:seanceId\",\n            element: /*#__PURE__*/_jsxDEV(WhiteboardPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 62\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 57\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/etablissement/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(EtablissementDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 65\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/createur/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(CreateurDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 60\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/formateur/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(FormateurDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 61\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/etudiant/dashboard\",\n            element: /*#__PURE__*/_jsxDEV(EtudiantDashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 60\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"student\",\n            element: /*#__PURE__*/_jsxDEV(StudentLandingPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"student/program/:programId\",\n            element: /*#__PURE__*/_jsxDEV(StudentProgramPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 67\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"feedback\",\n            element: /*#__PURE__*/_jsxDEV(FeedbackPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 49\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/EditProfile/:id\",\n            element: /*#__PURE__*/_jsxDEV(EditProfilePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 57\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/ProfilePage/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProfilePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 57\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(Auth, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 40\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            index: true,\n            element: /*#__PURE__*/_jsxDEV(LoginPage, {\n              setUser: setUser\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 39\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/forgot-password/\",\n            element: /*#__PURE__*/_jsxDEV(ForgetPasswordPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 58\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"ResetPasswordPage\",\n            element: /*#__PURE__*/_jsxDEV(ResetPasswordPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 58\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/reset-success\",\n            element: /*#__PURE__*/_jsxDEV(ResetSuccessPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 55\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/verify-sms\",\n            element: /*#__PURE__*/_jsxDEV(VerifyAccountPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/test-feedback\",\n          element: /*#__PURE__*/_jsxDEV(TestFeedback, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 51\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(NotFound, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 38\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 198,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"28naz3W8g3JCM0FrrnE5Lj4vSDA=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useEffect", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "useTranslation", "LoginPage", "ForgetPasswordPage", "ResetPasswordPage", "ResetSuccessPage", "NotFound", "HomePage", "FeedbackPage", "StudentLandingPage", "StudentProgramPage", "ProfilePage", "EditProfilePage", "<PERSON><PERSON>", "Main", "UsersPages", "AddUserView", "UserList", "TestFeedback", "ProgramsPage", "AddProgramList", "ModulePage", "AddModuleView", "CoursesPage", "ContenusPage", "AddQuizForm", "PlayQuizPage", "BuildProgramView", "BuildProgramOverviewPage", "ModuleList", "EditProgramView", "SessionPage", "EditQuizForm", "VerifyAccountPage", "SeanceFormateurPage", "AddSeanceFormateurView", "SeanceFormateurList", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SessionDetail", "SessionFeedbackList", "JitsiRoom", "<PERSON><PERSON><PERSON>", "WhiteboardPage", "AdminDashboard", "EtablissementDashboard", "CreateurDashboard", "FormateurDashboard", "EtudiantDashboard", "Spinner", "ToastContainer", "FaRegMoon", "GoSun", "jsxDEV", "_jsxDEV", "App", "_s", "user", "setUser", "darkMode", "setDarkMode", "loading", "setLoading", "loadUserFromStorage", "console", "log", "userStr", "localStorage", "getItem", "storageType", "sessionStorage", "JSON", "parse", "email", "role", "updatedUserStr", "stringify", "setItem", "err", "error", "removeItem", "userData", "handleStorageChange", "e", "key", "newValue", "handleUserLogout", "window", "addEventListener", "removeEventListener", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "height", "animation", "variant", "path", "element", "index", "roomName", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/App.js"], "sourcesContent": ["import { useEffect, useState } from \"react\";\r\nimport { BrowserRouter, Routes, Route } from \"react-router-dom\";\r\nimport './i18n';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n// Pages\r\nimport LoginPage from \"./pages/LoginPage\";\r\nimport ForgetPasswordPage from \"./pages/ForgetPasswordPage\";\r\nimport ResetPasswordPage from \"./pages/ResetPasswordPage\";\r\nimport ResetSuccessPage from \"./pages/ResetSuccessPage\";\r\n\r\nimport NotFound from \"./pages/NotFoundPage\";\r\nimport HomePage from \"./pages/HomePage\";\r\nimport FeedbackPage from \"./pages/FeedbackPage\";\r\nimport StudentLandingPage from \"./pages/StudentLandingPage\";\r\nimport StudentProgramPage from \"./pages/StudentProgramPage\";\r\nimport ProfilePage from \"./pages/ProfilePage\";\r\nimport EditProfilePage from \"./pages/EditProfilePage\";\r\n\r\n// Auth / Main/chatbot containers\r\nimport Auth from \"./apps/Auth\";\r\nimport Main from \"./apps/Main\";\r\n\r\n\r\n// User-related pages\r\nimport UsersPages from \"./pages/users/UsersPages\";\r\nimport AddUserView from \"./pages/users/views/AddUserView\";\r\nimport UserList from \"./pages/users/views/UserList\";\r\n\r\n// Test component\r\nimport TestFeedback from \"./TestFeedback\";\r\n\r\n\r\n// Program / Module / Course/ quiz / session\r\nimport ProgramsPage from \"./pages/ProgramsPage\";\r\nimport AddProgramList from \"./pages/users/views/AddProgramList\";\r\nimport ModulePage from \"./pages/ModulePage\";\r\nimport AddModuleView from \"./pages/users/views/AddModuleView\";\r\nimport CoursesPage from \"./pages/CoursesPage\";\r\nimport ContenusPage from \"./pages/ContenusPage\";\r\nimport AddQuizForm from \"./pages/users/views/AddQuizForm\";\r\nimport PlayQuizPage from \"./pages/users/views/PlayQuizPage\";\r\n\r\nimport BuildProgramView from \"./pages/users/views/BuildProgramView\";\r\nimport BuildProgramOverviewPage from \"./pages/BuildProgramOverviewPage\";\r\nimport ModuleList from './pages/users/views/ModuleList';\r\nimport EditProgramView from \"./pages/users/views/EditProgramView\";\r\nimport SessionPage from \"./pages/SessionPage\";\r\nimport EditQuizForm from \"./pages/users/views/EditQuizForm\";\r\nimport VerifyAccountPage from './pages/VerifyAccountPage';\r\nimport SeanceFormateurPage from \"./pages/SeanceFormateurPage\";\r\nimport AddSeanceFormateurView from \"./pages/users/views/AddSeanceFormateurView\";\r\nimport SeanceFormateurList from \"./pages/users/views/SeanceFormateurList\";\r\nimport AnimerSeanceView from \"./pages/users/views/AnimerSeanceView\";\r\nimport SessionDetail from \"./pages/SessionDetail\";\r\nimport SessionFeedbackList from './pages/users/views/SessionFeedbackList';\r\n\r\n\r\nimport JitsiRoom from './components/JitsiRoom';\r\nimport Chatbot from './components/Chatbot';\r\n// import TestChatPage from \"./pages/TestChatPage\";\r\nimport WhiteboardPage from \"./pages/WhiteboardPage\";\r\n\r\n//Dashboards\r\nimport AdminDashboard from \"./pages/users/views/AdminDashboard\";\r\nimport EtablissementDashboard from \"./pages/users/views/EtablissementDashboard\";\r\nimport CreateurDashboard from \"./pages/users/views/CreateurDashboard\";\r\nimport FormateurDashboard from \"./pages/users/views/FormateurDashboard\";\r\nimport EtudiantDashboard from \"./pages/users/views/EtudiantDashboard\";\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n// UI\r\nimport Spinner from \"react-bootstrap/Spinner\";\r\nimport { ToastContainer } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\n\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { FaRegMoon } from \"react-icons/fa\";\r\nimport { GoSun } from \"react-icons/go\";\r\n\r\n\r\nfunction App() {\r\n  const [user, setUser] = useState(null);\r\n  const [darkMode, setDarkMode] = useState(false);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n\r\n\r\n  const loadUserFromStorage = () => {\r\n    console.log(\"=== LOADING USER FROM STORAGE ===\");\r\n\r\n    // Logique simplifiée : vérifier localStorage puis sessionStorage\r\n    let userStr = localStorage.getItem(\"user\");\r\n    let storageType = \"localStorage\";\r\n\r\n    if (!userStr) {\r\n      userStr = sessionStorage.getItem(\"user\");\r\n      storageType = \"sessionStorage\";\r\n    }\r\n\r\n    if (!userStr) {\r\n      console.log(\"❌ No user found in storage\");\r\n      return null;\r\n    }\r\n\r\n    try {\r\n      const user = JSON.parse(userStr);\r\n      console.log(`✅ User loaded from ${storageType}:`, user.email);\r\n\r\n      // Vérifier si l'utilisateur a un rôle, sinon définir \"Etudiant\" par défaut\r\n      if (!user.role || user.role === \"user\") {\r\n        user.role = \"Etudiant\";\r\n      }\r\n\r\n      // Cas spécial pour l'utilisateur khalil\r\n      if (user.email === \"<EMAIL>\" && user.role !== \"Admin\") {\r\n        user.role = \"Admin\";\r\n        // Mettre à jour les données utilisateur dans le storage approprié\r\n        const updatedUserStr = JSON.stringify(user);\r\n        if (storageType === \"localStorage\") {\r\n          localStorage.setItem(\"user\", updatedUserStr);\r\n        } else {\r\n          sessionStorage.setItem(\"user\", updatedUserStr);\r\n        }\r\n      }\r\n\r\n      return user;\r\n    } catch (err) {\r\n      console.error(\"❌ Error parsing stored user:\", err);\r\n      // En cas d'erreur, nettoyer les storages\r\n      localStorage.removeItem(\"user\");\r\n      sessionStorage.removeItem(\"user\");\r\n      return null;\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    console.log(\"=== APP INITIALIZATION ===\");\r\n\r\n    // Charger l'utilisateur depuis le storage\r\n    const userData = loadUserFromStorage();\r\n\r\n    if (userData) {\r\n      console.log(\"✅ User loaded from storage:\", userData.email);\r\n      setUser(userData);\r\n    } else {\r\n      console.log(\"❌ No user found in storage\");\r\n      setUser(null);\r\n    }\r\n\r\n    setLoading(false);\r\n    console.log(\"=== APP INITIALIZATION COMPLETED ===\");\r\n  }, []);\r\n\r\n  // Écouter les événements de déconnexion\r\n  useEffect(() => {\r\n    const handleStorageChange = (e) => {\r\n      console.log(\"Storage change detected:\", e.key, e.newValue);\r\n\r\n      // Si les données utilisateur ont été supprimées, mettre à jour l'état\r\n      if (e.key === 'user' && !e.newValue) {\r\n        console.log(\"User data removed from storage, logging out...\");\r\n        setUser(null);\r\n      }\r\n    };\r\n\r\n    const handleUserLogout = () => {\r\n      console.log(\"User logout event received\");\r\n      setUser(null);\r\n    };\r\n\r\n    // Écouter les changements de localStorage\r\n    window.addEventListener('storage', handleStorageChange);\r\n\r\n    // Écouter l'événement personnalisé de déconnexion\r\n    window.addEventListener('userLogout', handleUserLogout);\r\n\r\n    // Nettoyer les écouteurs d'événements\r\n    return () => {\r\n      window.removeEventListener('storage', handleStorageChange);\r\n      window.removeEventListener('userLogout', handleUserLogout);\r\n    };\r\n  }, []);\r\n\r\n\r\n\r\n  return (\r\n    <div className={`${darkMode ? \"text-white bg-dark position-fixed h-100 w-100\" : \"\"}`}>\r\n      <ToastContainer />\r\n      <div className=\"d-flex justify-content-end\">\r\n        <button className=\"btn btn-light d-flex align-items-center\" onClick={() => setDarkMode(!darkMode)}>\r\n          {darkMode ? <GoSun /> : <FaRegMoon />}\r\n        </button>\r\n      </div>\r\n      {user && <Chatbot />}\r\n\r\n      {loading ? (\r\n        <div className=\"d-flex justify-content-center align-items-center\" style={{ height: \"80vh\" }}>\r\n          <Spinner animation=\"border\" variant=\"primary\" />\r\n        </div>\r\n      ) : (\r\n        <BrowserRouter>\r\n          <Routes>\r\n            {/* Route de connexion toujours disponible */}\r\n            <Route path=\"/login\" element={<LoginPage setUser={setUser} />} />\r\n\r\n            {user ? (\r\n              <Route path=\"/\" element={<Main setUser={setUser} user={user} />}>\r\n                <Route index element={<HomePage />} />\r\n                <Route path=\"users\" element={<UsersPages />}>\r\n                  <Route index element={<UserList />} />\r\n                  <Route path=\"add\" element={<AddUserView />} />\r\n                </Route>\r\n\r\n\r\n                <Route path=\"programs\" element={<ProgramsPage />} />\r\n                <Route path=\"programs/add\" element={<AddProgramList />} />\r\n                <Route path=\"module\" element={<ModulePage />} />\r\n                <Route path=\"module/add\" element={<AddModuleView />} />\r\n                <Route path=\"courses/*\" element={<CoursesPage />} />\r\n                <Route path=\"contenus/*\" element={<ContenusPage />} />\r\n\r\n                <Route path=\"/programs/overview\" element={<BuildProgramOverviewPage />} />\r\n\r\n                <Route path=\"/quizzes/create/:contenuId\" element={<AddQuizForm />} />\r\n                <Route path=\"/quizzes/play/:contenuId\" element={<PlayQuizPage />} />\r\n\r\n                <Route path=\"/programs/build/:programId\" element={<BuildProgramView />} />\r\n                <Route path=\"/programs/overview/:programId\" element={<BuildProgramOverviewPage />} />\r\n                <Route path=\"/modules\" element={<ModuleList />} />\r\n                <Route path=\"/programs/edit/:programId\" element={<EditProgramView />} />\r\n                <Route path=\"/sessions\" element={<SessionPage />} />\r\n                <Route path=\"/quizzes/edit/:contenuId\" element={<EditQuizForm />} />\r\n                <Route path=\"/formateur/seances\" element={<SeanceFormateurPage />} />\r\n                <Route path=\"/seances-formateur/add\" element={<AddSeanceFormateurView />} />\r\n                <Route path=\"/seances-formateur\" element={<SeanceFormateurList />} />\r\n\r\n                <Route path=\"/formateur/seances\" element={<SeanceFormateurPage />} />\r\n                <Route path=\"/sessions/:sessionId/seances\" element={<SeanceFormateurPage />} />\r\n                <Route path=\"/sessions/:sessionId/feedbacklist\" element={<SessionFeedbackList />} />\r\n\r\n\r\n                <Route path=\"/formateur/seance/:id\" element={<AnimerSeanceView />} />\r\n\r\n                <Route path=\"/jitsi\" element={<JitsiRoom roomName=\"majd-room\" />} />\r\n                {/* <Route path=\"/test-chat\" element={<TestChatPage />} /> */}\r\n                <Route path=\"/whiteboard/:seanceId\" element={<WhiteboardPage />} />\r\n\r\n                {/* //dashboard */}\r\n                <Route path=\"/admin/dashboard\" element={<AdminDashboard />} />\r\n                <Route path=\"/etablissement/dashboard\" element={<EtablissementDashboard />} />\r\n                <Route path=\"/createur/dashboard\" element={<CreateurDashboard />} />\r\n                <Route path=\"/formateur/dashboard\" element={<FormateurDashboard />} />\r\n                <Route path=\"/etudiant/dashboard\" element={<EtudiantDashboard />} />\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n                {/* Student */}\r\n                <Route path=\"student\" element={<StudentLandingPage />} />\r\n                <Route path=\"student/program/:programId\" element={<StudentProgramPage />} />\r\n                <Route path=\"feedback\" element={<FeedbackPage />} />\r\n                <Route path=\"/EditProfile/:id\" element={<EditProfilePage />} />\r\n                <Route path=\"/ProfilePage/:id\" element={<ProfilePage />} />\r\n              </Route>\r\n            ) : (\r\n              <Route path=\"/\" element={<Auth />}>\r\n                <Route index element={<LoginPage setUser={setUser} />} />\r\n                <Route path=\"/forgot-password/\" element={<ForgetPasswordPage />} />\r\n                <Route path=\"ResetPasswordPage\" element={<ResetPasswordPage />} />\r\n                <Route path=\"/reset-success\" element={<ResetSuccessPage />} />\r\n                <Route path=\"/verify-sms\" element={<VerifyAccountPage />} />\r\n\r\n              </Route>\r\n            )}\r\n\r\n\r\n\r\n            <Route path=\"/test-feedback\" element={<TestFeedback />} />\r\n            <Route path=\"*\" element={<NotFound />} />\r\n          </Routes>\r\n        </BrowserRouter>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAC/D,OAAO,QAAQ;AACf,SAASC,cAAc,QAAQ,eAAe;;AAE9C;AACA,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,gBAAgB,MAAM,0BAA0B;AAEvD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,eAAe,MAAM,yBAAyB;;AAErD;AACA,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAOC,IAAI,MAAM,aAAa;;AAG9B;AACA,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,WAAW,MAAM,iCAAiC;AACzD,OAAOC,QAAQ,MAAM,8BAA8B;;AAEnD;AACA,OAAOC,YAAY,MAAM,gBAAgB;;AAGzC;AACA,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,WAAW,MAAM,iCAAiC;AACzD,OAAOC,YAAY,MAAM,kCAAkC;AAE3D,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,wBAAwB,MAAM,kCAAkC;AACvE,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,OAAOC,sBAAsB,MAAM,4CAA4C;AAC/E,OAAOC,mBAAmB,MAAM,yCAAyC;AACzE,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,mBAAmB,MAAM,yCAAyC;AAGzE,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C;AACA,OAAOC,cAAc,MAAM,wBAAwB;;AAEnD;AACA,OAAOC,cAAc,MAAM,oCAAoC;AAC/D,OAAOC,sBAAsB,MAAM,4CAA4C;AAC/E,OAAOC,iBAAiB,MAAM,uCAAuC;AACrE,OAAOC,kBAAkB,MAAM,wCAAwC;AACvE,OAAOC,iBAAiB,MAAM,uCAAuC;;AAarE;AACA,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAE9C,OAAO,sCAAsC;AAC7C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGvC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC6D,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC+D,OAAO,EAAEC,UAAU,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAI5C,MAAMiE,mBAAmB,GAAGA,CAAA,KAAM;IAChCC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;IAEhD;IACA,IAAIC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC1C,IAAIC,WAAW,GAAG,cAAc;IAEhC,IAAI,CAACH,OAAO,EAAE;MACZA,OAAO,GAAGI,cAAc,CAACF,OAAO,CAAC,MAAM,CAAC;MACxCC,WAAW,GAAG,gBAAgB;IAChC;IAEA,IAAI,CAACH,OAAO,EAAE;MACZF,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzC,OAAO,IAAI;IACb;IAEA,IAAI;MACF,MAAMR,IAAI,GAAGc,IAAI,CAACC,KAAK,CAACN,OAAO,CAAC;MAChCF,OAAO,CAACC,GAAG,CAAC,sBAAsBI,WAAW,GAAG,EAAEZ,IAAI,CAACgB,KAAK,CAAC;;MAE7D;MACA,IAAI,CAAChB,IAAI,CAACiB,IAAI,IAAIjB,IAAI,CAACiB,IAAI,KAAK,MAAM,EAAE;QACtCjB,IAAI,CAACiB,IAAI,GAAG,UAAU;MACxB;;MAEA;MACA,IAAIjB,IAAI,CAACgB,KAAK,KAAK,kBAAkB,IAAIhB,IAAI,CAACiB,IAAI,KAAK,OAAO,EAAE;QAC9DjB,IAAI,CAACiB,IAAI,GAAG,OAAO;QACnB;QACA,MAAMC,cAAc,GAAGJ,IAAI,CAACK,SAAS,CAACnB,IAAI,CAAC;QAC3C,IAAIY,WAAW,KAAK,cAAc,EAAE;UAClCF,YAAY,CAACU,OAAO,CAAC,MAAM,EAAEF,cAAc,CAAC;QAC9C,CAAC,MAAM;UACLL,cAAc,CAACO,OAAO,CAAC,MAAM,EAAEF,cAAc,CAAC;QAChD;MACF;MAEA,OAAOlB,IAAI;IACb,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZd,OAAO,CAACe,KAAK,CAAC,8BAA8B,EAAED,GAAG,CAAC;MAClD;MACAX,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;MAC/BV,cAAc,CAACU,UAAU,CAAC,MAAM,CAAC;MACjC,OAAO,IAAI;IACb;EACF,CAAC;EAEDnF,SAAS,CAAC,MAAM;IACdmE,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;;IAEzC;IACA,MAAMgB,QAAQ,GAAGlB,mBAAmB,CAAC,CAAC;IAEtC,IAAIkB,QAAQ,EAAE;MACZjB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEgB,QAAQ,CAACR,KAAK,CAAC;MAC1Df,OAAO,CAACuB,QAAQ,CAAC;IACnB,CAAC,MAAM;MACLjB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzCP,OAAO,CAAC,IAAI,CAAC;IACf;IAEAI,UAAU,CAAC,KAAK,CAAC;IACjBE,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;EACrD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApE,SAAS,CAAC,MAAM;IACd,MAAMqF,mBAAmB,GAAIC,CAAC,IAAK;MACjCnB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkB,CAAC,CAACC,GAAG,EAAED,CAAC,CAACE,QAAQ,CAAC;;MAE1D;MACA,IAAIF,CAAC,CAACC,GAAG,KAAK,MAAM,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;QACnCrB,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;QAC7DP,OAAO,CAAC,IAAI,CAAC;MACf;IACF,CAAC;IAED,MAAM4B,gBAAgB,GAAGA,CAAA,KAAM;MAC7BtB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzCP,OAAO,CAAC,IAAI,CAAC;IACf,CAAC;;IAED;IACA6B,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEN,mBAAmB,CAAC;;IAEvD;IACAK,MAAM,CAACC,gBAAgB,CAAC,YAAY,EAAEF,gBAAgB,CAAC;;IAEvD;IACA,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEP,mBAAmB,CAAC;MAC1DK,MAAM,CAACE,mBAAmB,CAAC,YAAY,EAAEH,gBAAgB,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAIN,oBACEhC,OAAA;IAAKoC,SAAS,EAAE,GAAG/B,QAAQ,GAAG,+CAA+C,GAAG,EAAE,EAAG;IAAAgC,QAAA,gBACnFrC,OAAA,CAACJ,cAAc;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBzC,OAAA;MAAKoC,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzCrC,OAAA;QAAQoC,SAAS,EAAC,yCAAyC;QAACM,OAAO,EAAEA,CAAA,KAAMpC,WAAW,CAAC,CAACD,QAAQ,CAAE;QAAAgC,QAAA,EAC/FhC,QAAQ,gBAAGL,OAAA,CAACF,KAAK;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGzC,OAAA,CAACH,SAAS;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EACLtC,IAAI,iBAAIH,OAAA,CAACZ,OAAO;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAEnBlC,OAAO,gBACNP,OAAA;MAAKoC,SAAS,EAAC,kDAAkD;MAACO,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAP,QAAA,eAC1FrC,OAAA,CAACL,OAAO;QAACkD,SAAS,EAAC,QAAQ;QAACC,OAAO,EAAC;MAAS;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC,gBAENzC,OAAA,CAACvD,aAAa;MAAA4F,QAAA,eACZrC,OAAA,CAACtD,MAAM;QAAA2F,QAAA,gBAELrC,OAAA,CAACrD,KAAK;UAACoG,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEhD,OAAA,CAACnD,SAAS;YAACuD,OAAO,EAAEA;UAAQ;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAEhEtC,IAAI,gBACHH,OAAA,CAACrD,KAAK;UAACoG,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEhD,OAAA,CAACvC,IAAI;YAAC2C,OAAO,EAAEA,OAAQ;YAACD,IAAI,EAAEA;UAAK;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,gBAC9DrC,OAAA,CAACrD,KAAK;YAACsG,KAAK;YAACD,OAAO,eAAEhD,OAAA,CAAC9C,QAAQ;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtCzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,OAAO;YAACC,OAAO,eAAEhD,OAAA,CAACtC,UAAU;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,gBAC1CrC,OAAA,CAACrD,KAAK;cAACsG,KAAK;cAACD,OAAO,eAAEhD,OAAA,CAACpC,QAAQ;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtCzC,OAAA,CAACrD,KAAK;cAACoG,IAAI,EAAC,KAAK;cAACC,OAAO,eAAEhD,OAAA,CAACrC,WAAW;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eAGRzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEhD,OAAA,CAAClC,YAAY;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,cAAc;YAACC,OAAO,eAAEhD,OAAA,CAACjC,cAAc;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1DzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEhD,OAAA,CAAChC,UAAU;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEhD,OAAA,CAAC/B,aAAa;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvDzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEhD,OAAA,CAAC9B,WAAW;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,YAAY;YAACC,OAAO,eAAEhD,OAAA,CAAC7B,YAAY;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEtDzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,oBAAoB;YAACC,OAAO,eAAEhD,OAAA,CAACzB,wBAAwB;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE1EzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,4BAA4B;YAACC,OAAO,eAAEhD,OAAA,CAAC5B,WAAW;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,0BAA0B;YAACC,OAAO,eAAEhD,OAAA,CAAC3B,YAAY;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEpEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,4BAA4B;YAACC,OAAO,eAAEhD,OAAA,CAAC1B,gBAAgB;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1EzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,+BAA+B;YAACC,OAAO,eAAEhD,OAAA,CAACzB,wBAAwB;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrFzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEhD,OAAA,CAACxB,UAAU;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,2BAA2B;YAACC,OAAO,eAAEhD,OAAA,CAACvB,eAAe;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEhD,OAAA,CAACtB,WAAW;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,0BAA0B;YAACC,OAAO,eAAEhD,OAAA,CAACrB,YAAY;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,oBAAoB;YAACC,OAAO,eAAEhD,OAAA,CAACnB,mBAAmB;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,wBAAwB;YAACC,OAAO,eAAEhD,OAAA,CAAClB,sBAAsB;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5EzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,oBAAoB;YAACC,OAAO,eAAEhD,OAAA,CAACjB,mBAAmB;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAErEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,oBAAoB;YAACC,OAAO,eAAEhD,OAAA,CAACnB,mBAAmB;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,8BAA8B;YAACC,OAAO,eAAEhD,OAAA,CAACnB,mBAAmB;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/EzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,mCAAmC;YAACC,OAAO,eAAEhD,OAAA,CAACd,mBAAmB;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGpFzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,uBAAuB;YAACC,OAAO,eAAEhD,OAAA,CAAChB,gBAAgB;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAErEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEhD,OAAA,CAACb,SAAS;cAAC+D,QAAQ,EAAC;YAAW;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEpEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,uBAAuB;YAACC,OAAO,eAAEhD,OAAA,CAACX,cAAc;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGnEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,kBAAkB;YAACC,OAAO,eAAEhD,OAAA,CAACV,cAAc;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,0BAA0B;YAACC,OAAO,eAAEhD,OAAA,CAACT,sBAAsB;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9EzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,qBAAqB;YAACC,OAAO,eAAEhD,OAAA,CAACR,iBAAiB;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,sBAAsB;YAACC,OAAO,eAAEhD,OAAA,CAACP,kBAAkB;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,qBAAqB;YAACC,OAAO,eAAEhD,OAAA,CAACN,iBAAiB;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAapEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,SAAS;YAACC,OAAO,eAAEhD,OAAA,CAAC5C,kBAAkB;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzDzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,4BAA4B;YAACC,OAAO,eAAEhD,OAAA,CAAC3C,kBAAkB;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5EzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,UAAU;YAACC,OAAO,eAAEhD,OAAA,CAAC7C,YAAY;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,kBAAkB;YAACC,OAAO,eAAEhD,OAAA,CAACzC,eAAe;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,kBAAkB;YAACC,OAAO,eAAEhD,OAAA,CAAC1C,WAAW;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,gBAERzC,OAAA,CAACrD,KAAK;UAACoG,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEhD,OAAA,CAACxC,IAAI;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,gBAChCrC,OAAA,CAACrD,KAAK;YAACsG,KAAK;YAACD,OAAO,eAAEhD,OAAA,CAACnD,SAAS;cAACuD,OAAO,EAAEA;YAAQ;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzDzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,mBAAmB;YAACC,OAAO,eAAEhD,OAAA,CAAClD,kBAAkB;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,mBAAmB;YAACC,OAAO,eAAEhD,OAAA,CAACjD,iBAAiB;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClEzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,gBAAgB;YAACC,OAAO,eAAEhD,OAAA,CAAChD,gBAAgB;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DzC,OAAA,CAACrD,KAAK;YAACoG,IAAI,EAAC,aAAa;YAACC,OAAO,eAAEhD,OAAA,CAACpB,iBAAiB;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEvD,CACR,eAIDzC,OAAA,CAACrD,KAAK;UAACoG,IAAI,EAAC,gBAAgB;UAACC,OAAO,eAAEhD,OAAA,CAACnC,YAAY;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DzC,OAAA,CAACrD,KAAK;UAACoG,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEhD,OAAA,CAAC/C,QAAQ;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAChB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACvC,EAAA,CAnNQD,GAAG;AAAAkD,EAAA,GAAHlD,GAAG;AAqNZ,eAAeA,GAAG;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}