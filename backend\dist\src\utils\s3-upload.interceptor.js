"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.S3UploadInterceptor = S3UploadInterceptor;
const common_1 = require("@nestjs/common");
const operators_1 = require("rxjs/operators");
const s3_service_1 = require("../s3/s3.service");
function S3UploadInterceptor(options = {}) {
    let MixinInterceptor = class MixinInterceptor {
        s3Service;
        constructor(s3Service) {
            this.s3Service = s3Service;
        }
        async intercept(context, next) {
            const request = context.switchToHttp().getRequest();
            const file = request.file;
            if (!file) {
                return next.handle();
            }
            if (options.allowedMimeTypes && !options.allowedMimeTypes.includes(file.mimetype)) {
                throw new Error(`Type de fichier non autorisé. Types autorisés: ${options.allowedMimeTypes.join(', ')}`);
            }
            if (options.maxFileSize && file.size > options.maxFileSize) {
                throw new Error(`Fichier trop volumineux. Taille maximale: ${options.maxFileSize / (1024 * 1024)}MB`);
            }
            try {
                const result = await this.s3Service.uploadFile(file, options.folder || 'uploads', options.customFileName);
                request.file = {
                    ...file,
                    filename: result.key,
                    path: result.url,
                    destination: 's3',
                    s3Url: result.url,
                    s3Key: result.key
                };
                return next.handle().pipe((0, operators_1.map)(data => {
                    if (data && typeof data === 'object') {
                        const d = data;
                        if (d.fileUrl) {
                            d.fileUrl = result.url;
                        }
                        if (d.url) {
                            d.url = result.url;
                        }
                        if (d.s3Url) {
                            d.s3Url = result.url;
                        }
                        if (d.s3Key) {
                            d.s3Key = result.key;
                        }
                    }
                    return data;
                }));
            }
            catch (error) {
                throw new Error(`Erreur lors de l'upload vers S3: ${error.message}`);
            }
        }
    };
    MixinInterceptor = __decorate([
        (0, common_1.Injectable)(),
        __metadata("design:paramtypes", [s3_service_1.S3Service])
    ], MixinInterceptor);
    const Interceptor = (0, common_1.mixin)(MixinInterceptor);
    return Interceptor;
}
//# sourceMappingURL=s3-upload.interceptor.js.map