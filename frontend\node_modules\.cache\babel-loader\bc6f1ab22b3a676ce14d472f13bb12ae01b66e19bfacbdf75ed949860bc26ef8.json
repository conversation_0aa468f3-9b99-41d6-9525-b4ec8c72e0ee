{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\TestFeedback.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Button, Box, Typography, Alert } from '@mui/material';\nimport AddSessionFeedback from './pages/users/views/AddSessionFeedback';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestFeedback = () => {\n  _s();\n  const [open, setOpen] = useState(false);\n  const [message, setMessage] = useState('');\n\n  // Simuler un utilisateur connecté\n  React.useEffect(() => {\n    const mockUser = {\n      id: 2,\n      email: \"<EMAIL>\",\n      name: \"Test User\",\n      role: \"Etudiant\"\n    };\n    localStorage.setItem(\"user\", JSON.stringify(mockUser));\n  }, []);\n  const mockSession = {\n    id: 1,\n    title: \"Formation React Avancé\",\n    description: \"Session de formation sur React et ses concepts avancés\"\n  };\n  const handleOpen = () => {\n    setMessage('');\n    setOpen(true);\n  };\n  const handleClose = () => {\n    setOpen(false);\n    setMessage('Formulaire fermé');\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3,\n      maxWidth: 600,\n      mx: 'auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Test du Formulaire d'\\xC9valuation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      onClick: handleOpen,\n      size: \"large\",\n      sx: {\n        mb: 2\n      },\n      children: \"Ouvrir le formulaire d'\\xE9valuation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"text.secondary\",\n      children: [\"Session de test: \", mockSession.title]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AddSessionFeedback, {\n      open: open,\n      onClose: handleClose,\n      session: mockSession\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(TestFeedback, \"7/LNLafE+BOvoy4sAU0RRYuVc6w=\");\n_c = TestFeedback;\nexport default TestFeedback;\nvar _c;\n$RefreshReg$(_c, \"TestFeedback\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON>", "Box", "Typography", "<PERSON><PERSON>", "AddSessionFeedback", "jsxDEV", "_jsxDEV", "TestFeedback", "_s", "open", "<PERSON><PERSON><PERSON>", "message", "setMessage", "useEffect", "mockUser", "id", "email", "name", "role", "localStorage", "setItem", "JSON", "stringify", "mockSession", "title", "description", "handleOpen", "handleClose", "sx", "p", "max<PERSON><PERSON><PERSON>", "mx", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "mb", "onClick", "size", "color", "onClose", "session", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/TestFeedback.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Button, Box, Typography, Alert } from '@mui/material';\nimport AddSessionFeedback from './pages/users/views/AddSessionFeedback';\n\nconst TestFeedback = () => {\n  const [open, setOpen] = useState(false);\n  const [message, setMessage] = useState('');\n\n  // Simuler un utilisateur connecté\n  React.useEffect(() => {\n    const mockUser = {\n      id: 2,\n      email: \"<EMAIL>\",\n      name: \"Test User\",\n      role: \"Etudiant\"\n    };\n    localStorage.setItem(\"user\", JSON.stringify(mockUser));\n  }, []);\n\n  const mockSession = {\n    id: 1,\n    title: \"Formation React Avancé\",\n    description: \"Session de formation sur React et ses concepts avancés\"\n  };\n\n  const handleOpen = () => {\n    setMessage('');\n    setOpen(true);\n  };\n\n  const handleClose = () => {\n    setOpen(false);\n    setMessage('Formulaire fermé');\n  };\n\n  return (\n    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Test du Formulaire d'Évaluation\n      </Typography>\n      \n      {message && (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          {message}\n        </Alert>\n      )}\n      \n      <Button \n        variant=\"contained\" \n        onClick={handleOpen}\n        size=\"large\"\n        sx={{ mb: 2 }}\n      >\n        Ouvrir le formulaire d'évaluation\n      </Button>\n      \n      <Typography variant=\"body2\" color=\"text.secondary\">\n        Session de test: {mockSession.title}\n      </Typography>\n      \n      <AddSessionFeedback\n        open={open}\n        onClose={handleClose}\n        session={mockSession}\n      />\n    </Box>\n  );\n};\n\nexport default TestFeedback;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,GAAG,EAAEC,UAAU,EAAEC,KAAK,QAAQ,eAAe;AAC9D,OAAOC,kBAAkB,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACAD,KAAK,CAACe,SAAS,CAAC,MAAM;IACpB,MAAMC,QAAQ,GAAG;MACfC,EAAE,EAAE,CAAC;MACLC,KAAK,EAAE,2BAA2B;MAClCC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE;IACR,CAAC;IACDC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACR,QAAQ,CAAC,CAAC;EACxD,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,WAAW,GAAG;IAClBR,EAAE,EAAE,CAAC;IACLS,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE;EACf,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBd,UAAU,CAAC,EAAE,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMiB,WAAW,GAAGA,CAAA,KAAM;IACxBjB,OAAO,CAAC,KAAK,CAAC;IACdE,UAAU,CAAC,kBAAkB,CAAC;EAChC,CAAC;EAED,oBACEN,OAAA,CAACL,GAAG;IAAC2B,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,QAAQ,EAAE,GAAG;MAAEC,EAAE,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC3C1B,OAAA,CAACJ,UAAU;MAAC+B,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZ3B,OAAO,iBACNL,OAAA,CAACH,KAAK;MAACoC,QAAQ,EAAC,MAAM;MAACX,EAAE,EAAE;QAAEY,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,EAClCrB;IAAO;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAEDhC,OAAA,CAACN,MAAM;MACLiC,OAAO,EAAC,WAAW;MACnBQ,OAAO,EAAEf,UAAW;MACpBgB,IAAI,EAAC,OAAO;MACZd,EAAE,EAAE;QAAEY,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,EACf;IAED;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEThC,OAAA,CAACJ,UAAU;MAAC+B,OAAO,EAAC,OAAO;MAACU,KAAK,EAAC,gBAAgB;MAAAX,QAAA,GAAC,mBAChC,EAACT,WAAW,CAACC,KAAK;IAAA;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAEbhC,OAAA,CAACF,kBAAkB;MACjBK,IAAI,EAAEA,IAAK;MACXmC,OAAO,EAAEjB,WAAY;MACrBkB,OAAO,EAAEtB;IAAY;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9B,EAAA,CA/DID,YAAY;AAAAuC,EAAA,GAAZvC,YAAY;AAiElB,eAAeA,YAAY;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}