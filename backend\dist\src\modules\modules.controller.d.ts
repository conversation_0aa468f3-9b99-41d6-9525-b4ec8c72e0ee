import { ModulesService } from './modules.service';
import { CreateModuleDto } from './dto/create-module.dto';
export declare class ModulesController {
    private readonly modulesService;
    constructor(modulesService: ModulesService);
    create(dto: CreateModuleDto): import(".prisma/client").Prisma.Prisma__ModuleClient<{
        id: number;
        name: string;
        periodUnit: import(".prisma/client").$Enums.PeriodUnit;
        duration: number;
    }, never, import("@prisma/client/runtime/library").DefaultArgs, import(".prisma/client").Prisma.PrismaClientOptions>;
    findAll(): import(".prisma/client").Prisma.PrismaPromise<({
        programs: ({
            program: {
                name: string;
            };
        } & {
            id: number;
            programId: number;
            moduleId: number;
        })[];
        buildProgramModules: ({
            buildProgram: {
                program: {
                    name: string;
                };
            } & {
                id: number;
                createdAt: Date | null;
                programId: number;
                startDate: Date | null;
                endDate: Date | null;
                imageUrl: string | null;
                level: string;
            };
        } & {
            id: number;
            buildProgramId: number;
            moduleId: number;
        })[];
    } & {
        id: number;
        name: string;
        periodUnit: import(".prisma/client").$Enums.PeriodUnit;
        duration: number;
    })[]>;
    remove(id: string): import(".prisma/client").Prisma.Prisma__ModuleClient<{
        id: number;
        name: string;
        periodUnit: import(".prisma/client").$Enums.PeriodUnit;
        duration: number;
    }, never, import("@prisma/client/runtime/library").DefaultArgs, import(".prisma/client").Prisma.PrismaClientOptions>;
}
