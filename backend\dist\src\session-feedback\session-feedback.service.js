"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionFeedbackService = void 0;
const common_1 = require("@nestjs/common");
const nestjs_prisma_1 = require("nestjs-prisma");
let SessionFeedbackService = class SessionFeedbackService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(dto) {
        const { sessionId, userId, feedback, sessionComments, trainerComments, teamComments, suggestions, ratings, ...rest } = dto;
        if (!userId)
            throw new common_1.BadRequestException('userId requis');
        if (!sessionId)
            throw new common_1.BadRequestException('sessionId requis');
        const user = await this.prisma.user.findUnique({ where: { id: userId } });
        if (!user)
            throw new common_1.NotFoundException(`Utilisateur ${userId} introuvable`);
        const ratingValues = ratings ? Object.values(ratings)
            .filter((v) => typeof v === 'number' && v >= 1 && v <= 5) : [];
        const averageRating = ratingValues.length > 0
            ? parseFloat((ratingValues.reduce((a, b) => a + b, 0) / ratingValues.length).toFixed(1))
            : 0;
        const existingFeedback = await this.prisma.sessionFeedback.findFirst({
            where: { sessionId, userId },
            orderBy: { createdAt: 'desc' }
        });
        if (existingFeedback) {
            await this.prisma.sessionFeedback.update({
                where: { id: existingFeedback.id },
                data: {
                    rating: averageRating,
                    comments: rest.comments,
                    ratings: ratings ? JSON.stringify(ratings) : null,
                    formData: JSON.stringify(rest),
                    user: { connect: { id: userId } }
                },
            });
            await this.prisma.sessionFeedbackList.updateMany({
                where: { sessionId, userId },
                data: {
                    feedback: feedback || this.generateFeedbackMessage({ ratings, ...rest }),
                    nom: user.name ?? '',
                    email: user.email,
                    sessionComments,
                    trainerComments,
                    teamComments,
                    suggestions,
                },
            });
        }
        else {
            await this.prisma.$transaction([
                this.prisma.sessionFeedback.create({
                    data: {
                        sessionId,
                        userId,
                        rating: averageRating,
                        comments: rest.comments,
                        ratings: ratings ? JSON.stringify(ratings) : null,
                        formData: JSON.stringify(rest),
                        user: { connect: { id: userId } }
                    }
                }),
                this.prisma.sessionFeedbackList.create({
                    data: {
                        sessionId,
                        userId,
                        feedback: feedback || this.generateFeedbackMessage({ ratings, ...rest }),
                        nom: user.name ?? '',
                        email: user.email,
                        sessionComments,
                        trainerComments,
                        teamComments,
                        suggestions,
                    },
                }),
            ]);
        }
        await this.cleanupOldFeedbacks(sessionId);
        return {
            message: 'Feedback enregistré avec succès',
            averageRating: averageRating,
        };
    }
    generateFeedbackMessage(dto) {
        const parts = [];
        if (dto.ratings) {
            const ratingEntries = Object.entries(dto.ratings)
                .filter(([_, value]) => typeof value === 'number' && value >= 1 && value <= 5);
            if (ratingEntries.length > 0) {
                const avg = ratingEntries.reduce((sum, [_, value]) => sum + value, 0) / ratingEntries.length;
                parts.push(`Note moyenne: ${avg.toFixed(1)}/5`);
            }
        }
        if (dto.sessionComments)
            parts.push(`Commentaires sur la session: ${dto.sessionComments}`);
        if (dto.trainerComments)
            parts.push(`Commentaires sur le formateur: ${dto.trainerComments}`);
        if (dto.teamComments)
            parts.push(`Commentaires sur l'équipe: ${dto.teamComments}`);
        if (dto.suggestions)
            parts.push(`Suggestions: ${dto.suggestions}`);
        if (dto.improvementAreas)
            parts.push(`Zones d'amélioration: ${dto.improvementAreas}`);
        if (dto.wouldRecommend)
            parts.push(`Recommanderait: ${dto.wouldRecommend}`);
        return parts.join('\n\n') || 'Feedback de session soumis';
    }
    async cleanupOldFeedbacks(sessionId) {
        const allFeedbacks = await this.prisma.sessionFeedbackList.findMany({
            where: { sessionId },
            orderBy: { createdAt: 'desc' },
        });
        const latestMap = new Map();
        allFeedbacks.forEach(fb => {
            if (!latestMap.has(fb.userId))
                latestMap.set(fb.userId, fb.id);
        });
        const idsToDelete = allFeedbacks
            .filter(fb => latestMap.get(fb.userId) !== fb.id)
            .map(fb => fb.id);
        if (idsToDelete.length > 0) {
            await this.prisma.sessionFeedbackList.deleteMany({
                where: { id: { in: idsToDelete } }
            });
        }
    }
    async getSessionFeedbacks(sessionId) {
        const feedbacks = await this.prisma.sessionFeedback.findMany({
            where: { sessionId },
            orderBy: { createdAt: 'desc' },
            include: { user: true },
        });
        return feedbacks.map(fb => {
            let coordinates = null;
            try {
                const commentsData = JSON.parse(fb.comments || '{}');
                if (commentsData.coordinates) {
                    coordinates = commentsData.coordinates;
                }
            }
            catch {
            }
            return {
                ...fb,
                studentName: fb.user?.name || '',
                studentEmail: fb.user?.email || '',
                coordinates,
            };
        });
    }
    async getStudentFeedbacks(sessionId, userId) {
        const feedbackList = await this.prisma.sessionFeedbackList.findMany({
            where: { sessionId, userId },
            orderBy: { createdAt: 'desc' },
            include: { user: true },
        });
        const sessionFeedbacks = await this.prisma.sessionFeedback.findMany({
            where: { sessionId, userId },
            orderBy: { createdAt: 'desc' },
            include: { user: true },
        });
        const combinedFeedbacks = feedbackList.map(fb => {
            const matchingRating = sessionFeedbacks.find(sf => Math.abs(new Date(sf.createdAt).getTime() - new Date(fb.createdAt).getTime()) < 60000);
            let parsedRatings = null;
            let parsedFormData = null;
            try {
                if (matchingRating?.ratings) {
                    parsedRatings = JSON.parse(matchingRating.ratings);
                }
                if (matchingRating?.formData) {
                    parsedFormData = JSON.parse(matchingRating.formData);
                }
            }
            catch (error) {
                console.error('Error parsing JSON data:', error);
            }
            return {
                id: fb.id,
                sessionId: fb.sessionId,
                userId: fb.userId,
                rating: matchingRating?.rating || null,
                comments: fb.feedback,
                feedback: fb.feedback,
                sessionComments: fb.sessionComments,
                trainerComments: fb.trainerComments,
                teamComments: fb.teamComments,
                suggestions: fb.suggestions,
                ratings: parsedRatings,
                formData: parsedFormData,
                createdAt: fb.createdAt,
                studentName: fb.nom || fb.user?.name || '',
                studentEmail: fb.email || fb.user?.email || '',
                user: fb.user,
            };
        });
        return combinedFeedbacks;
    }
    async getSessionFeedbackList(sessionId) {
        const feedbacks = await this.prisma.sessionFeedbackList.findMany({
            where: { sessionId },
            orderBy: { createdAt: 'desc' },
        });
        const uniqueFeedbacks = new Map();
        feedbacks.forEach(fb => {
            if (fb.email && !uniqueFeedbacks.has(fb.email)) {
                uniqueFeedbacks.set(fb.email, fb);
            }
        });
        const results = await Promise.all([...uniqueFeedbacks.values()].map(async (fb) => {
            const userRatings = await this.prisma.sessionFeedback.findMany({
                where: { sessionId: fb.sessionId, userId: fb.userId },
                select: { rating: true },
            });
            const validRatings = userRatings
                .map(r => r.rating)
                .filter((r) => typeof r === 'number' && r >= 1 && r <= 5);
            const averageRating = validRatings.length > 0
                ? parseFloat((validRatings.reduce((a, b) => a + b, 0) / validRatings.length).toFixed(1))
                : null;
            let emoji = '⭐';
            if (averageRating !== null) {
                if (averageRating >= 4.5)
                    emoji = '🤩';
                else if (averageRating >= 3.5)
                    emoji = '😊';
                else if (averageRating >= 2.5)
                    emoji = '🙂';
                else if (averageRating >= 1.5)
                    emoji = '😐';
                else
                    emoji = '😞';
            }
            return {
                id: fb.id,
                userId: fb.userId,
                studentName: fb.nom,
                studentEmail: fb.email,
                fullFeedback: fb.feedback,
                averageRating,
                emoji,
            };
        }));
        return results;
    }
    async getStats() {
        const feedbacks = await this.prisma.sessionFeedback.findMany({
            include: { user: true },
        });
        const totalFeedbacks = feedbacks.length;
        const totalRating = feedbacks.reduce((sum, fb) => sum + (fb.rating || 0), 0);
        const averageRating = totalFeedbacks > 0
            ? parseFloat((totalRating / totalFeedbacks).toFixed(1))
            : 0;
        return {
            totalFeedbacks,
            averageRating,
            ratingDistribution: this.getRatingDistribution(feedbacks)
        };
    }
    getRatingDistribution(feedbacks) {
        const distribution = {
            '5': 0,
            '4': 0,
            '3': 0,
            '2': 0,
            '1': 0
        };
        feedbacks.forEach(fb => {
            if (fb.rating >= 4.5)
                distribution['5']++;
            else if (fb.rating >= 3.5)
                distribution['4']++;
            else if (fb.rating >= 2.5)
                distribution['3']++;
            else if (fb.rating >= 1.5)
                distribution['2']++;
            else if (fb.rating >= 0)
                distribution['1']++;
        });
        return distribution;
    }
    async getAnalytics(range = '6months') {
        const feedbacks = await this.prisma.sessionFeedback.findMany({
            include: { user: true },
        });
        const filteredFeedbacks = this.filterByTimeRange(feedbacks, range);
        return {
            averageRating: filteredFeedbacks.length > 0
                ? parseFloat((filteredFeedbacks.reduce((sum, fb) => sum + (fb.rating || 0), 0) / filteredFeedbacks.length).toFixed(1))
                : 0,
            ratingDistribution: this.getRatingDistribution(filteredFeedbacks),
            timelineData: this.generateTimelineData(filteredFeedbacks, range)
        };
    }
    filterByTimeRange(feedbacks, range) {
        const now = new Date();
        let cutoff;
        switch (range) {
            case '7days':
                cutoff = new Date(now.setDate(now.getDate() - 7));
                break;
            case '30days':
                cutoff = new Date(now.setDate(now.getDate() - 30));
                break;
            case '3months':
                cutoff = new Date(now.setMonth(now.getMonth() - 3));
                break;
            case '6months':
                cutoff = new Date(now.setMonth(now.getMonth() - 6));
                break;
            case '1year':
                cutoff = new Date(now.setFullYear(now.getFullYear() - 1));
                break;
            default:
                return feedbacks;
        }
        return feedbacks.filter(fb => new Date(fb.createdAt) >= cutoff);
    }
    generateTimelineData(feedbacks, range) {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        if (range === '7days' || range === '30days') {
            const dailyData = {};
            const days = range === '7days' ? 7 : 30;
            for (let i = 0; i < days; i++) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const label = `${date.getDate()} ${months[date.getMonth()]}`;
                dailyData[label] = 0;
            }
            feedbacks.forEach(fb => {
                const date = new Date(fb.createdAt);
                const label = `${date.getDate()} ${months[date.getMonth()]}`;
                if (dailyData[label] !== undefined)
                    dailyData[label]++;
            });
            return Object.entries(dailyData)
                .reverse()
                .map(([date, count]) => ({ date, count }));
        }
        const monthlyData = {};
        const monthCount = range === '3months' ? 3 : range === '6months' ? 6 : 12;
        for (let i = 0; i < monthCount; i++) {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            const label = `${months[date.getMonth()]} ${date.getFullYear()}`;
            monthlyData[label] = 0;
        }
        feedbacks.forEach(fb => {
            const date = new Date(fb.createdAt);
            const label = `${months[date.getMonth()]} ${date.getFullYear()}`;
            if (monthlyData[label] !== undefined)
                monthlyData[label]++;
        });
        return Object.entries(monthlyData)
            .reverse()
            .map(([month, count]) => ({ month, count }));
    }
};
exports.SessionFeedbackService = SessionFeedbackService;
exports.SessionFeedbackService = SessionFeedbackService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [nestjs_prisma_1.PrismaService])
], SessionFeedbackService);
//# sourceMappingURL=session-feedback.service.js.map