"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeanceFeedbackController = void 0;
const common_1 = require("@nestjs/common");
const seance_feedback_service_1 = require("./seance-feedback.service");
let SeanceFeedbackController = class SeanceFeedbackController {
    seanceFeedbackService;
    constructor(seanceFeedbackService) {
        this.seanceFeedbackService = seanceFeedbackService;
    }
    async createSeanceFeedback(dto) {
        return this.seanceFeedbackService.createSeanceFeedback(dto);
    }
    async getFeedbackList(seanceId) {
        if (!seanceId) {
            throw new common_1.BadRequestException('seanceId is required');
        }
        return this.seanceFeedbackService.getFeedbackList(+seanceId);
    }
    async getFeedbackDetails(seanceId, userId) {
        if (!seanceId || !userId) {
            throw new common_1.BadRequestException('seanceId and userId are required');
        }
        return this.seanceFeedbackService.getFeedbackDetails(+seanceId, +userId);
    }
    async cleanupOldFeedbackList(seanceId) {
        if (!seanceId) {
            throw new common_1.BadRequestException('seanceId is required');
        }
        return this.seanceFeedbackService.cleanupOldFeedbacks(+seanceId);
    }
    async cleanupFeedbackList(seanceId, emailsToKeep) {
        if (!seanceId) {
            throw new common_1.BadRequestException('seanceId is required');
        }
        if (!Array.isArray(emailsToKeep)) {
            throw new common_1.BadRequestException('emailsToKeep must be an array of strings');
        }
        return this.seanceFeedbackService.deleteFeedbackListNotInEmails(+seanceId, emailsToKeep);
    }
};
exports.SeanceFeedbackController = SeanceFeedbackController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SeanceFeedbackController.prototype, "createSeanceFeedback", null);
__decorate([
    (0, common_1.Get)('feedbacklist/:seanceId'),
    __param(0, (0, common_1.Param)('seanceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SeanceFeedbackController.prototype, "getFeedbackList", null);
__decorate([
    (0, common_1.Get)('details/:seanceId/:userId'),
    __param(0, (0, common_1.Param)('seanceId')),
    __param(1, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], SeanceFeedbackController.prototype, "getFeedbackDetails", null);
__decorate([
    (0, common_1.Delete)('cleanup-old/:seanceId'),
    __param(0, (0, common_1.Param)('seanceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SeanceFeedbackController.prototype, "cleanupOldFeedbackList", null);
__decorate([
    (0, common_1.Delete)('cleanup/:seanceId'),
    __param(0, (0, common_1.Param)('seanceId')),
    __param(1, (0, common_1.Body)('emailsToKeep')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Array]),
    __metadata("design:returntype", Promise)
], SeanceFeedbackController.prototype, "cleanupFeedbackList", null);
exports.SeanceFeedbackController = SeanceFeedbackController = __decorate([
    (0, common_1.Controller)('feedback/seance'),
    __metadata("design:paramtypes", [seance_feedback_service_1.SeanceFeedbackService])
], SeanceFeedbackController);
//# sourceMappingURL=seance-feedback.controller.js.map