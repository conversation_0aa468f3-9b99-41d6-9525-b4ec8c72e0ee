import React from 'react';
import { Box, Typography, Card, CardContent, Grid } from '@mui/material';

const TestAverageRating = () => {
  // Fonction de test pour calculer et formater les moyennes
  const testAverageCalculation = (ratings, expectedAvg, expectedDisplay) => {
    // Simuler le calcul comme dans le code réel
    const validRatings = Object.values(ratings)
      .filter(rating => typeof rating === 'number' && rating >= 1 && rating <= 5);
    
    let finalRating = 0;
    if (validRatings.length > 0) {
      const sum = validRatings.reduce((acc, rating) => acc + rating, 0);
      finalRating = sum / validRatings.length;
    }
    
    // Formater comme dans le code réel
    const formatRating = (rating) => {
      const rounded = Math.round(rating * 10) / 10;
      return rounded % 1 === 0 ? rounded.toString() : rounded.toFixed(1).replace('.', ',');
    };
    
    const formattedRating = formatRating(finalRating);
    
    // Déterminer emoji et couleur
    let emoji, label, color;
    if (finalRating >= 4.5) {
      emoji = '🤩'; label = 'Excellent'; color = '#4caf50';
    } else if (finalRating >= 3.5) {
      emoji = '😊'; label = 'Bon'; color = '#8bc34a';
    } else if (finalRating >= 2.5) {
      emoji = '🙂'; label = 'Moyen'; color = '#ff9800';
    } else if (finalRating >= 1.5) {
      emoji = '😐'; label = 'Mauvais'; color = '#ff5722';
    } else {
      emoji = '😞'; label = 'Très mauvais'; color = '#f44336';
    }
    
    return {
      ratings: validRatings,
      calculatedAvg: finalRating,
      expectedAvg,
      formattedDisplay: `${formattedRating}/5`,
      expectedDisplay,
      emoji,
      label,
      color,
      isCorrect: Math.abs(finalRating - expectedAvg) < 0.01 && `${formattedRating}/5` === expectedDisplay
    };
  };

  // Cas de test
  const testCases = [
    {
      name: "Moyenne entière 4.0",
      ratings: { overallRating: 4, contentRelevance: 4, learningObjectives: 4, sessionStructure: 4 },
      expectedAvg: 4.0,
      expectedDisplay: "4/5"
    },
    {
      name: "Moyenne décimale 4.3",
      ratings: { overallRating: 4, contentRelevance: 5, learningObjectives: 4, sessionStructure: 4 },
      expectedAvg: 4.25,
      expectedDisplay: "4,3/5"
    },
    {
      name: "Moyenne décimale 3.7",
      ratings: { overallRating: 4, contentRelevance: 3, learningObjectives: 4, sessionStructure: 4 },
      expectedAvg: 3.75,
      expectedDisplay: "3,8/5"
    },
    {
      name: "Moyenne décimale 3.5",
      ratings: { overallRating: 3, contentRelevance: 4, learningObjectives: 3, sessionStructure: 4 },
      expectedAvg: 3.5,
      expectedDisplay: "3,5/5"
    },
    {
      name: "Moyenne décimale 2.3",
      ratings: { overallRating: 2, contentRelevance: 3, learningObjectives: 2, sessionStructure: 2 },
      expectedAvg: 2.25,
      expectedDisplay: "2,3/5"
    },
    {
      name: "Moyenne haute 4.8",
      ratings: { overallRating: 5, contentRelevance: 5, learningObjectives: 4, sessionStructure: 5 },
      expectedAvg: 4.75,
      expectedDisplay: "4,8/5"
    }
  ];

  const results = testCases.map(testCase => testAverageCalculation(
    testCase.ratings, 
    testCase.expectedAvg, 
    testCase.expectedDisplay
  ));

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        🧪 Test des Moyennes Décimales - Average Rating
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        Vérification que les moyennes décimales s'affichent correctement dans la colonne Average Rating.
      </Typography>

      <Grid container spacing={2}>
        {testCases.map((testCase, index) => {
          const result = results[index];
          return (
            <Grid item xs={12} md={6} key={index}>
              <Card sx={{ 
                border: result.isCorrect ? '2px solid #4caf50' : '2px solid #f44336',
                bgcolor: result.isCorrect ? '#f1f8e9' : '#ffebee'
              }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {result.isCorrect ? '✅' : '❌'} {testCase.name}
                  </Typography>
                  
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Notes individuelles:</strong> {result.ratings.join(', ')}
                  </Typography>
                  
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Moyenne calculée:</strong> {result.calculatedAvg.toFixed(2)}
                  </Typography>
                  
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Affichage:</strong> 
                    <span style={{ fontSize: '1.2rem', marginLeft: '8px' }}>
                      {result.emoji} {result.formattedDisplay}
                    </span>
                    <span style={{ color: result.color, marginLeft: '8px', fontWeight: 'bold' }}>
                      {result.label}
                    </span>
                  </Typography>
                  
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Attendu:</strong> {testCase.expectedDisplay}
                  </Typography>
                  
                  {!result.isCorrect && (
                    <Typography variant="body2" color="error">
                      ⚠️ Différence détectée !
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      <Box sx={{ mt: 4, p: 2, bgcolor: '#e3f2fd', borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          📊 Résumé des tests
        </Typography>
        <Typography variant="body1">
          Tests réussis: {results.filter(r => r.isCorrect).length} / {results.length}
        </Typography>
        {results.every(r => r.isCorrect) ? (
          <Typography variant="body1" color="success.main" sx={{ fontWeight: 'bold' }}>
            🎉 Tous les tests sont réussis ! Les moyennes décimales s'affichent correctement.
          </Typography>
        ) : (
          <Typography variant="body1" color="error.main" sx={{ fontWeight: 'bold' }}>
            ⚠️ Certains tests ont échoué. Vérifiez le code de formatage.
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default TestAverageRating;
